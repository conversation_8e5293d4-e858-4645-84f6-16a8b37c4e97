var jssdk = $jssdk;
var Controller = {
    appId: null,
    page_url: null,
    canNewPage: true,
    toThinkive: function(url, closeStackView) {
		jssdk.ready(function() {
			jssdk.toThinkive(url, closeStackView);
		});
	},
    locationReplace: function(url) {
        var channel_type =  comm.TOOLS.getCookie('channel_type') || ''
        
        if(this.getAppId() == 'yjbweb' && channel_type == '2005000000000') {
            if(history.replaceState && url.indexOf(location.origin) > -1){
                history.replaceState(null, document.title, url);
                history.go(0);
            }else{
                location.replace(url);
            }
        } else if(this.getAppId() == 'yjbweb' && channel_type == '2095000000000') {
            var sd_token = comm.TOOLS.getCookie('sd_token');
            url= url.indexOf('?') > 0 ? url + '&sd_token=' + sd_token : url+'?sd_token='+sd_token;
            location.replace(url);
        } else {
            location.replace(url); //测试新方案
        }
    },
    //获取app_id
    getAppId: function() {
        var appId = this.appId;
        if (appId == null || appId == "null" || !appId) {
            appId = comm.TOOLS.getCookie("app_id");
            //cookie被系统清除解决方案 (通过浏览器判断)
            if (appId == null || appId == "null" || !appId) {
                var ua = navigator.userAgent.toLocaleUpperCase();
                if (comm.TOOLS.isWX()) {
                    //微信端
                    appId = "yjbwx";
                } else if(ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1) {
                    // 是否在鸿蒙交易端内
                    appId = "yjbweb";
                } else {
                    //动态判定app_id
                    var isWKWebView = navigator.userAgent.indexOf('ios/wkwebview') > -1 ;
                    if(isWKWebView){
                        if (window.webkit
                            && window.webkit.messageHandlers
                            && window.webkit.messageHandlers.yjbInterface 
                            && window.webkit.messageHandlers.yjbInterface.postMessage) {
                            appId = "yjb3.0";
                        } else {
                            //默认其他
                            appId = "yjbweb";
                        }
                    }else{
                        if (window.yjbInterface 
                            &&window.yjbInterface.execute) {
                            appId = "yjb3.0";
                        } else {
                            //默认其他
                            appId = "yjbweb";
                        }
                    }
                }
                comm.TOOLS.setCookie("app_id", appId);
            }
        }
        return appId;
    },

    //JS 获取 绝对路径 layer(向前平移目录层级数)
    getPageUrl: function(layer) {
        var page_url = "";
        var curWwwPath = window.document.location.href;
        curWwwPath = curWwwPath.split("?")[0];
        //if(layer!=undefined){
        //    var pathArray=curWwwPath.split('/');
        //    var pathIndex=curWwwPath.indexOf(pathArray[pathArray.length-1-layer]);
        //    page_url=curWwwPath.substring(0,pathIndex);
        //}else{
        //    page_url=curWwwPath.substring(0,curWwwPath.lastIndexOf('/')+1);
        //}
        if (layer != undefined) {
            var pathArray = curWwwPath.split("/");
            var pathIndex = curWwwPath.lastIndexOf(
                pathArray[pathArray.length - 1 - layer]
            );
            page_url = curWwwPath.substring(0, pathIndex);
        } else {
            page_url = curWwwPath.substring(0, curWwwPath.lastIndexOf("/") + 1);
        }
        return page_url;
    },

    changeURL: function(str) {
        //str=this.getPageUrl()+str;
        // Tool.Loggercollect({  logtype: 'oldJump_changeURL', str: str, message: location.href});
        if (this.getAppId() == "yjbjyd") {
            var app = window.navigator.appVersion.toLocaleLowerCase();
            if (app.indexOf("windows phone") > 0) {
                window.external.notify(str);
            } else if (app.indexOf("iphone") > 0) {
                window.location.href = str;
            } else if (app.indexOf("android") > 0 && window.MyWebView && window.MyWebView.onJsOverrideUrlLoading) {
                window.MyWebView.onJsOverrideUrlLoading(str);
            } else {
                window.location.href = str;
            }
            
        } else if(Controller.getAppId() == 'yjbweb' && comm.TOOLS.getCookie('channel_type') == "2095000000000"){
            var sd_token = comm.TOOLS.getCookie('sd_token');
            str= str.indexOf('?') > 0 ? str + '&sd_token=' + sd_token : str+'?sd_token='+sd_token;
            Controller.zfbReady(function () {
                AlipayJSBridge.call('pushWindow', {
                    url: str,
                    param: {
                        readTitle: true,
                    }
                });
            });
        } else {
            window.location.href = str;
        }
    },

    //关闭当前页面打开新页面  str 为完整路径,跳转外链接专用
    basePageInit: function(str) {
        // Tool.Loggercollect({  logtype: 'oldJump_basePageInit', str: str, message: location.href});
        if (this.getAppId() == "yjbjyd") {
            if (comm.TOOLS.isYjbIos()) {
                this.changeURL("http://action:1964/?url=" + str);
            } else if (comm.TOOLS.isAndroid()) {
                this.changeURL("http://action:1964/?url=" + str);
            } else {
                this.changeURL("http://action:1964/?url=" + str);
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.open(
                    {
                        leftType: 1,
                        url: str,
                        rightType: 99,
                        rightText: "",
                        animated: 0,
                    },
                    true
                );
            });
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            //微信
            if (typeof isClose !== "undefined") {
                this.clearHistory();
                this.changeURL(str);
            } else {
                this.locationReplace(str)
                // location.replace(str); //测试新方案
            }
        }
    },

    //关闭当前页面打开新页面  layer(向前平移目录层级数)
    /*isRedirect:是否直接跳转*/
    pageInit: function(str, layer, isClose, isRedirect, openParam) {
        // Tool.Loggercollect({  logtype: 'oldJump_pageInit', str: str, message: location.href});
        if (this.getAppId() == "yjbjyd") {
            if (comm.TOOLS.isYjbIos()) {
                this.changeURL(
                    "http://action:1964/?url=" + this.getPageUrl(layer) + str
                );
            } else if (comm.TOOLS.isAndroid()) {
                this.changeURL(
                    "http://action:1964/?url=" + this.getPageUrl(layer) + str
                );
            } else {
                this.changeURL(
                    "http://action:1964/?url=" + this.getPageUrl(layer) + str
                );
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            var re = new RegExp("^((https|http)?://)");
            if (re.test(str) || isRedirect) {
                var gotoUrl = str;
            } else {
                var gotoUrl = this.getPageUrl(layer) + str;
            }
            var openOptions = {
                leftType: 1,
                url: gotoUrl,
                rightType: 99,
                rightText: "",
                animated: 0,
            };
            if (openParam && openParam.keepActive) {
                openOptions.keepActive = openParam.keepActive;
            }
            jssdk.ready(function() {
                jssdk.open(openOptions, true);
            });
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            //微信
            if (typeof isClose !== "undefined") {
                this.clearHistory();
                this.changeURL(this.getPageUrl(layer) + str);
            } else {
                this.locationReplace(this.getPageUrl(layer) + str)
                // location.replace(this.getPageUrl(layer) + str); //测试新方案
            }
        }
    },

    //打开新页面  str 为完整路径,跳转外链接专用
    baseNewPageInit: function(str, oConfig, title) {
        // Tool.Loggercollect({  logtype: 'oldJump_baseNewPageInit', str: str, message: location.href});
        var config = oConfig || {};
        var urlParam = "";
        if (this.getAppId() == "yjbjyd") {
            if (config.rightType) {
                urlParam +=
                    "&&secondtype=" +
                    config.rightType +
                    "&&secondtext=" +
                    config.rightText +
                    "&&secondjsfuncname=" +
                    config.rightJS;
            }
            if (comm.TOOLS.isYjbIos()) {
                this.changeURL(
                    "http://action:10061/?firsttype=10&&secondtype=9&&fullscreen=1" +
                        urlParam +
                        "&&url=" +
                        str
                );
            } else if (comm.TOOLS.isAndroid()) {
                this.changeURL(
                    "http://action:10061/?firsttype=10&&secondtype=9&&fullscreen=1" +
                        urlParam +
                        "&&url=" +
                        str
                );
            } else {
                this.changeURL(
                    "http://action:10061/?firsttype=10&&secondtype=9&&fullscreen=1" +
                        urlParam +
                        "&&url=" +
                        str
                );
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            //防止重复点击打开多个窗口
            if (this.canNewPage == false) {
                return false;
            }
            this.canNewPage = false;
            setTimeout(function() {
                Controller.canNewPage = true;
            }, 2000);
            var options = {
                leftType: 1,
                url: str,
                rightType: config.rightType || 99,
                rightText: config.rightText || "",
                rightJS: config.rightJS || "",
                animated: 0,
            };
            jssdk.ready(function() {
                if (title) {
                    options.title = title;
                }
                jssdk.open(options, false);
            });
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            this.changeURL(str);
        }
    },

    //打开新页面
    /*isRedirect:是否直接跳转*/
    newPageInit: function(str, layer, isRedirect, openParam) {
        // Tool.Loggercollect({  logtype: 'oldJump_newPageInit', str: str, message: location.href});
        if (this.getAppId() == "yjbjyd") {
            if (comm.TOOLS.isYjbIos()) {
                this.changeURL(
                    "http://action:10061/?firsttype=10&&secondtype=9&&fullscreen=1&&url=" +
                        this.getPageUrl(layer) +
                        str
                );
            } else if (comm.TOOLS.isAndroid()) {
                this.changeURL(
                    "http://action:10061/?firsttype=10&&secondtype=9&&fullscreen=1&&url=" +
                        this.getPageUrl(layer) +
                        str
                );
            } else {
                this.changeURL(
                    "http://action:10061/?firsttype=10&&secondtype=9&&fullscreen=1&&url=" +
                        this.getPageUrl(layer) +
                        str
                );
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            if (this.canNewPage == false) {
                return false;
            }
            this.canNewPage = false;
            setTimeout(function() {
                Controller.canNewPage = true;
            }, 2000);
            var re = new RegExp("^((https|http)?://)");
            if (re.test(str) || isRedirect) {
                var gotoUrl = str;
            } else {
                var gotoUrl = this.getPageUrl(layer) + str;
            }
            //alert('gotoUrl'+gotoUrl);
            jssdk.ready(function() {
                var openOptions = {
                    leftType: 1,
                    url: gotoUrl,
                    rightType: 99,
                    rightText: "",
                    animated: 0,
                };
                if (openParam && openParam.keepActive) {
                    openOptions.keepActive = openParam.keepActive;
                }
                jssdk.open(openOptions, false);
            });
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            this.changeURL(this.getPageUrl(layer) + str);
        }
    },

    toThinkivePage: function(url, openNewPage) {
		if (this.getAppId() == 'yjb3.0') {
            if (openNewPage) {
				this.toThinkive(url);
			} else {
				this.toThinkive(url, '1'); // 打开后关闭中转页
			}
		} else {
			if (openNewPage) {
				this.baseNewPageInit(url);
			} else {
				this.basePageInit(url);
			}
		}
	},

    //返回上一个页面
    goBackPage: function() {
        var ua = navigator.userAgent.toLocaleUpperCase();
        if (this.getAppId() == "yjbjyd") {
            if (comm.TOOLS.isYjbIos()) {
                this.changeURL("http://action:3413/?"); //关闭当前页面
            } else if (comm.TOOLS.isAndroid()) {
                this.changeURL("http://action:3413/?");
            } else {
                this.changeURL("http://action:3413/?");
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj" || 
            ( sessionStorage.getItem('channel_type') == '1013000000000' && // 谈指间渠道
            Controller.getAppId() == 'yjbweb' ) ||
            (ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1)
        ) {
            jssdk.ready(function() {
                jssdk.close();
            });
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            window.history.go(-1); //测试新方案
            try {
                //适配微信-当前页面为起始页
                setTimeout(function() {
                    WeixinJSBridge.call("closeWindow");
                }, 500);
            } catch (e) {}
        }
    },

    //关闭所有页面且交易端注销 (微信:有提示信息) :用于session超时
    /*
     *closeType:退出的类型
     * closeArgs：退出的参数
     * */
    closeAllPage: function(loginUri, closeType, closeArgs) {
        var self = this;
        var ua = navigator.userAgent.toLocaleUpperCase();
        var channel_type =  comm.TOOLS.getCookie('channel_type') || ''
        if (this.getAppId() == "yjbjyd") {
            if (comm.TOOLS.isYjbIos()) {
                //self.changeURL("http://action:10402/?context=&&url="+encodeURIComponent("http://action:10090/?loginType=1&&loginKind=0"));
                /**
                 * 改为入参可传递,需测试验证
                 * 2017.11.25 经验证2.0此方法失效,无法关闭webview,故注释临时采用安卓方案
                 */
                //self.changeURL("http://action:10402/?context=&&url="+encodeURIComponent(loginUri));

                var exp = new Date();
                exp.setTime(exp.getTime() + 2000); //仅存储2秒 以防杀进程
                document.cookie =
                    "cgjzq_" +
                    "androidback" +
                    "=" +
                    encodeURI("1") +
                    ";expires=" +
                    exp.toGMTString();
                //返回功能号
                self.changeURL("http://action:3413/?");
            } else if (comm.TOOLS.isAndroid()) {
                //适配安卓关闭所有view
                var exp = new Date();
                exp.setTime(exp.getTime() + 2000); //仅存储2秒 以防杀进程
                document.cookie =
                    "cgjzq_" +
                    "androidback" +
                    "=" +
                    encodeURI("1") +
                    ";expires=" +
                    exp.toGMTString();
                //返回功能号
                self.changeURL("http://action:3413/?");
                //配套在父亲
            } else {
                self.changeURL("http://action:10402/?");
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"||
            (ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1)
        ) {
            //通过closeType获取登录需要的nativeLoginType
            var nativeLoginType = 1; //默认退出---普通登录
            var closeType = closeType || 3; //默认退出到---普通交易首页
            if (+closeType == 3) {
                var nativeLoginType = 1;
            } else if (+closeType == 4) {
                var nativeLoginType = 2;
            }
            jssdk.ready(function() {
                jssdk.logout(
                    nativeLoginType,
                    function() {
                        jssdk.goBack(closeType, closeArgs);
                    },
                    function() {
                        jssdk.goBack(closeType, closeArgs);
                    }
                );
            });
        }else if(Controller.getAppId() == 'yjbweb' && channel_type == '2005000000000') {
            // 同花顺渠道特殊出处理
            if(location.href.indexOf('Ths_Token') == -1) {
                callNativeHandler(
                    "JSWangTingEvent",
                    {
                        action: "CloseWebVC",
                        param: {}
                    },
                    function () {}
                );
            }
        }else if(Controller.getAppId() == 'yjbweb' && channel_type == '2095000000000') {
            //支付宝渠道特殊出处理
            Controller.zfbReady(function () {
                AlipayJSBridge.call('popWindow');
            });
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            try {
                //适配微信
                setTimeout(function() {
                    WeixinJSBridge.call("closeWindow");
                }, 500);
            } catch (e) {}
            window.close();
        }
    },

    //清除返回历史 适配微信
    clearHistory: function() {
        if (this.getAppId() == "yjbjyd") {
            //暂时什么都不做
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            //微信 //todo yjbweb
            window.history.pushState(
                {
                    title: "close",
                    url: "close.html",
                },
                "close page",
                "close.html"
            );
        }
    },
    //调用登录框函数,用于登陆后刷新本页面
    /**
     * loginType : 登录类型  1 信用交易 0 普通交易
     * backUrl   : 回调URL等于页面自身
     * ssoLoginUrl : 微信登录必须填写,交易端可缺省
     * version: 获取sso的版本
     * webLoginUrl: web交易登陆地址
     */
    login: function(loginType, backUrl, ssoLoginUrl, version, webLoginUrl) {
        let ua = navigator.userAgent.toLocaleUpperCase(); // 如果鸿蒙系统 
        if (this.getAppId() == "yjbjyd") {
            var versionName = version == "2.0" ? 2 : 1;
            if (loginType == 1) {
                //信用交易登录
                this.changeURL(
                    "http://action:10090/?logintype=2&&loginKind=0&&url=" +
                        encodeURIComponent(
                            "http://action:1964/?url=/yjb/sso_control.htm?v=" +
                                versionName +
                                "&loginType=1&backUrl=" +
                                backUrl
                        )
                );
            } else {
                //普通交易登录
                this.changeURL(
                    "http://action:10090/?url=" +
                        encodeURIComponent(
                            "http://action:1964/?url=/yjb/sso_control.htm?v=" +
                                versionName +
                                "&loginType=0&backUrl=" +
                                backUrl
                        )
                );
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj" || 
            (ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1)
        ) {
            //为了兼容原来的account_type的传参方式，对传入的account_type参数做特殊处理
            if (+loginType == 0) {
                var nativeLoginType = 1;
            } else if (+loginType == 1) {
                var nativeLoginType = 2;
            }
            jssdk.ready(function() {
                jssdk.ukeyLogin(function(code) {
                    if (+code !== 0) {
                        jssdk.goBack();
                        return;
                    }

                    jssdk.login(nativeLoginType, backUrl, version);
                });
            });
        } else if (this.getAppId() == "yjbweb") {
            //todo yjbweb
            //yjbweb调用web登陆页，微信是否支持待定

            //ssoLoginUrl=ssoLoginUrl||'http://192.168.22.86/git/login/web/build/index.html';
            try {
                if (webLoginUrl) {
                    //accountType:
                    var url =
                        webLoginUrl +
                        "?app_id=" +
                        this.getAppId() +
                        "&accountType=" +
                        loginType +
                        "&backUrl=" +
                        encodeURIComponent(backUrl);
                } else {
                }
            } catch (e) {}
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            var url =
                ssoLoginUrl +
                "?app_id=" +
                this.getAppId() +
                "from=wechat&loginType=" +
                loginType +
                "&backUrl=" +
                encodeURIComponent(backUrl);
            this.changeURL(url);
        }
    },
    //调用登录框函数，无uKeylogin
    /**
     * loginType : 登录类型  1 信用交易 0 普通交易
     * backUrl   : 回调URL等于页面自身
     * ssoLoginUrl : 微信登录必须填写,交易端可缺省
     * version: 获取sso的版本
     */
    loginWithoutuKeylogin: function(loginType, backUrl, ssoLoginUrl, version) {
        if (this.getAppId() == "yjbjyd") {
            var versionName = version == "2.0" ? 2 : 1;
            if (loginType == 1) {
                //信用交易登录
                this.changeURL(
                    "http://action:10090/?logintype=2&&loginKind=0&&url=" +
                        encodeURIComponent(
                            "http://action:1964/?url=/yjb/sso_control.htm?v=" +
                                versionName +
                                "&loginType=1&backUrl=" +
                                backUrl
                        )
                );
            } else {
                //普通交易登录
                this.changeURL(
                    "http://action:10090/?url=" +
                        encodeURIComponent(
                            "http://action:1964/?url=/yjb/sso_control.htm?v=" +
                                versionName +
                                "&loginType=0&backUrl=" +
                                backUrl
                        )
                );
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            //为了兼容原来的account_type的传参方式，对传入的account_type参数做特殊处理
            if (+loginType == 0) {
                var nativeLoginType = 1;
            } else if (+loginType == 1) {
                var nativeLoginType = 2;
            }
            jssdk.ready(function() {
                jssdk.login(nativeLoginType, backUrl, version);
            });
        } else if (this.getAppId() == "yjbweb") {
            //todo yjbweb
            //yjbweb调用web登陆页，微信是否支持待定
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            var url =
                ssoLoginUrl +
                "?app_id=" +
                this.getAppId() +
                "from=wechat&loginType=" +
                loginType +
                "&backUrl=" +
                encodeURIComponent(backUrl);
            this.changeURL(url);
        }
    },
    //调用登录框函数 ,用于登录后打开新页面
    /**
     * loginType : 登录类型  1 信用交易 0 普通交易
     * backUrl   : 回调URL等于页面自身
     * ssoLoginUrl : 微信登录必须填写,交易端可缺省
     */
    loginNewPage: function() {
        if (this.getAppId() == "yjbjyd") {
            if (loginType == 1) {
                //信用交易登录
                this.changeURL(
                    "http://action:10090/?logintype=2&&loginKind=0&&url=" +
                        encodeURIComponent(
                            "http://action:1964/?url=/yjb/sso_control.htm?loginType=1&backUrl=" +
                                backUrl
                        )
                );
            } else {
                //普通交易登录
                if (comm.TOOLS.isYjbIos()) {
                    this.changeURL(
                        "http://action:10090/?url=" +
                            encodeURIComponent(
                                "http://action:1964/?url=/yjb/sso_control.htm?loginType=0&backUrl=" +
                                    backUrl
                            )
                    );
                } else if (comm.TOOLS.isAndroid()) {
                    this.changeURL(
                        "http://action:10090/?url=" +
                            encodeURIComponent(
                                "http://action:10061/?url=/yjb/sso_control.htm?loginType=0&backUrl=" +
                                    backUrl
                            )
                    );
                } else {
                    this.changeURL(
                        "http://action:10090/?url=" +
                            encodeURIComponent(
                                "http://action:1964/?url=/yjb/sso_control.htm?loginType=0&backUrl=" +
                                    backUrl
                            )
                    );
                }
            }
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            //AAAAA暂时没有适配
        } else if (this.getAppId() == "yjbweb") {
            //todo yjbweb
            //yjbweb调用web登陆页，微信是否支持待定
        } else {
            //getAppId()=='yjbwx' 或 yjbweb 或其他
            var url =
                ssoLoginUrl +
                "?app_id=" +
                this.getAppId() +
                "from=wechat&loginType=" +
                loginType +
                "&backUrl=" +
                encodeURIComponent(backUrl);
            this.changeURL(url);
        }
    },

    /***********
     设置页面的title
     params:obj --- 页面的相关参数
     ********/
    setTitle: function(params) {
        if (this.getAppId() == "yjbjyd") {
            //2.0需要特殊方法适配
        } else if (this.getAppId() == "yjbwx") {
            //微信
            $("title").html(params.title);
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj" || 
            ( sessionStorage.getItem('channel_type') == '1013000000000' && // 谈指间渠道
            Controller.getAppId() == 'yjbweb' )
        ) {
            $("title").html(params.title);
            jssdk.ready(function() {
                jssdk.setTitle(params);
            });
        } else if (Controller.getAppId() == 'yjbweb' && comm.TOOLS.getCookie('channel_type') == "2095000000000") {
            //支付宝渠道特殊出处理
            Controller.zfbReady(function () {
                AlipayJSBridge.call('setTitle', {title: params.title});
            });
        } else if (this.getAppId() == "yjbweb") {
            $("title").html(params.title);
        } else {
        }
    },

    /***********
    拨打电话
    phonenum:string -- 电话号码
    ********/
    callphone: function(phonenum) {
        var phonenum = phonenum || "95310";
        if (this.getAppId() == "yjbjyd") {
            this.changeURL("tel:" + phonenum);
        } else if (this.getAppId() == "yjbwx") {
            //微信(待确认实现)
            this.changeURL("tel:" + phonenum);
        } else if (this.getAppId() == "yjbweb") {
            this.changeURL("tel:" + phonenum);
        } else if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.call(phonenum);
            });
        } else {
        }
    },

    /***********
     页面注册事件
     callback:func --页面的事件名称
     ********/
    onAppear: function(callback) {
        if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.onAppear(callback);
            });
        } else if(Controller.getAppId() == 'yjbweb' && comm.TOOLS.getCookie('channel_type') == "2095000000000"){
            document.addEventListener('resume', callback, false);
        } else {
        }
    },

    /***********
     页面注册事件
     callback:func --页面的事件名称
     ********/
     onDisappear: function(callback) {
        if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.onDisappear(callback);
            });
        } else {
        }
    },

    /***********
     * 完成适当性测评,关闭webview，继续登录前的操作
     * @param {any} loginType 登录类型 见：login
     * @param {any} idValid  是否完成：0-未完成，1-已完成
     */
    doneSuitability: function(loginType, isValid) {
        if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            if (+loginType == 0) {
                var nativeLoginType = 1;
            } else if (+loginType == 1) {
                var nativeLoginType = 2;
            } else if (+loginType == 15) {
                var nativeLoginType = 16;
            }
            jssdk.ready(function() {
                jssdk.doneSuitability(nativeLoginType, isValid);
            });
        } else {
        }
    },

    chooseImg: function(param, succFunc) {
        if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.chooseImg(param, succFunc);
            });
        } else {
        }
    },
    /***********
     * @param {string} content 显示内容
     * @param {function} fn 回调函数
     */
    showAlert: function(content, title, fn) {
        if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.showAlert(content, title || "确定", fn);
            });
        } else {
            Dialog.showAlert(content, title, fn);
        }
    },

    /***********
     * 经纪业务权限开通提示
     * @param {string} content 显示内容
     */
    permissionOpenAlert:function(content, title){
        content = content || '权限开通30日内我司会向您推送回访问卷，请您务必配合完成。请您结合本人风险承受能力问卷测评结果，以及投资偏好、承受损失等因素，审慎参与。若我司从业人员存在接受全权委托、违规操作投资者账户等行为，请及时拨打95310反馈。';
        title = title || '确认';
        this.showAlert(content, title);
    },

    /***********
     * @param {string} content 显示内容
     * @param {number} closeTime 消失时间
     * * @param {function} fn 回调函数
     */
    showNotice: function(content, closeTime, fn) {
        if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.showAlert(content, title || "确定", fn);
            });
        } else {
            Dialog.showNotice(content, closeTime);
        }
    },

    /***********
     * 弹出确认提示框
     * @function showConfirm
     * @param {string} content 显示内容
     * @param {function} onConfirm 点击确定后回调
     * @param {function} onCancel 点击取消后回调
     */
    showConfirm: function(content, btnName, onConfirm, onCancel) {
        if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.showConfirm(content, onConfirm, onCancel);
            });
        } else {
            Dialog.confirm(content, btnName, onConfirm, onCancel);
        }
    },

    /**
     * IOS 2.0 native bug适配,该方法用于 登录页面等同于回调页面的场景,解决IOS多重复页面bug
     */
    loginHackForIOS2: function() {
        // comm.BASEVAR.ISIOS 仅能判断IOS系统,而 comm.TOOLS.isYjbIos() 可判断确定是佣金宝2.0 IOS
        if (comm.TOOLS.isYjbIos()) {
            var time = comm.TOOLS.GetQueryString("t");
            if (time == null) {
                $("body").hide();
                //2---错误打开的没用的登录页面
                comm.TOOLS.setCookie("lhi2" + time, "");
                //注意,因为这里页面的加载反应比经过桥接处理的文件要快,所以要gobackonload特别延迟处理
                window.GoBackOnLoad = function() {
                    //关闭页面
                    //清除hackcode所使用的cookie - 避免head头过长问题
                    var date = new Date();
                    date.setTime(-1000);
                    var data = document.cookie;
                    var dataArray = data.split("; ");
                    for (var i = 0; i < dataArray.length; i++) {
                        var varName = dataArray[i].split("=")[0];
                        //判断varName 包含关键字
                        if (varName.substr(0, 10) == "cgjzq_lhi2") {
                            document.cookie =
                                varName + "=''; expires=" + date.toGMTString();
                        }
                    }
                    Controller.changeURL("http://action:3413/?");
                };
                return;
            }
            if (comm.TOOLS.getCookie("lhi2" + time) == "1") {
                /**
                 * 步骤2 完成了此webview的回退操作,所以删除cookie的逻辑迁移至步骤2,但是阻断页面加载的代码保留
                 */
                ////关闭页面
                this.changeURL("http://action:3413/?");
            } else {
                $("body").show();
                //1---正确进入登录页面时
                comm.TOOLS.setCookie("lhi2" + time, "1");
            }
        }
    },
    /***********
     * 行情跳转
     * @function quotation
     * @param {string} stockCode 个股代码
     * @param {function} tabId 表情页
     */
    quotation: function(stockCode, tabId) {
        if (
            this.getAppId() == "yjb3.0" ||
            this.getAppId() == "yjbsa" ||
            this.getAppId() == "thjj"
        ) {
            jssdk.ready(function() {
                jssdk.quotation(stockCode, tabId);
            });
        } else {
            this.changeURL("http://action:12051/?stockcode=" + stockCode);
        }
    },
    /***********
     * 支付宝JSAPI调用桥接
     * @function quotation
     * @param {string} stockCode 个股代码
     * @param {function} tabId 表情页
     */
    zfbReady: function(callback) {
        // 如果jsbridge已经注⼊则直接调⽤
        if (window.AlipayJSBridge) {
            callback && callback();
        } else {
            // 如果没有注⼊则监听注⼊的事件
            document.addEventListener('AlipayJSBridgeReady', callback, false);
        }
    },
};
try {
    (function() {
        sessionStorage.setItem('channel_type', comm.TOOLS.getCookie('channel_type'));
        $(document).ready(function() {
            //if(Controller.getAppId()=='yjbweb'||comm.TOOLS.getCookie('app_id')==null){
            yjbWebHack();
            //}
        });

        function yjbWebHack() {
            //根据渠道号进行初始化
            const CHANNEL_LIST = {
                ths: "2005000000000",
                qqstock: "2003000000000",
                zfb: "2095000000000"
            };
            var initChannelType = function() {
                var key_name = "channel_type";
                var url_channel_type = comm.TOOLS.GetQueryString(
                    "channel_type"
                );
                var old_channel_type = sessionStorage.getItem(key_name);
                sessionStorage.setItem(
                    key_name,
                    url_channel_type || old_channel_type
                );
            };
            var getChannelType = function() {
                var key_name = "channel_type";
                return sessionStorage.getItem(key_name);
            };

            initChannelType();
            var currentChannelType = getChannelType();

            switch (currentChannelType) {
                case CHANNEL_LIST.ths:
                    // resetControllerForTHS();
                    break;
                case CHANNEL_LIST.qqstock:
                    resetControllerForQQSTOCK();
                case CHANNEL_LIST.zfb:
                    resetControllerForZFB();
                default:
                    break;
            }
            /**同花顺hack */
            function resetControllerForTHS() {
                //关闭提示
                var showCloseAlert = function() {
                    if (typeof Dialog !== "undefined") {
                        Dialog.showAlert("请点击左上角“关闭”退出本业务");
                    } else if (typeof layerUtils !== "undefined") {
                        layerUtils.alert(
                            '<div style="text-align:center">请点击左上角“关闭”退出本业务</div>',
                            function() {}
                        );
                    }
                };
                /**关闭全部页面 */
                Controller.closeAllPage = function(
                    loginUri,
                    closeType,
                    closeArgs
                ) {
                    showCloseAlert();
                };
                /**返回至上个页面 */
                Controller.goBackPage = function() {
                    showCloseAlert();
                };
                /**组织popstate 事件*/
                pushHistory();
                window.addEventListener(
                    "popstate",
                    function(e) {
                        showCloseAlert();
                        pushHistory();
                    },
                    false
                );
                function pushHistory() {
                    var sp = window.location.href.split("/");
                    var page = sp[sp.length - 1];
                    page = page.split(".")[0];
                    var state = {
                        title: "title",
                        url: "#",
                    };
                    window.history.pushState(state, "title", "#");
                }
            }

            /**腾讯自选股hack */
            function resetControllerForQQSTOCK() {
                Controller.closeAllPage = function(
                    loginUri,
                    closeType,
                    closeArgs
                ) {
                    execQQStockJSBridge(function() {
                        window.StockJSBridge.invoke("exit", {});
                    });
                    // var returl = sessionStorage.getItem('qqstock_returl');
                    // if(returl){
                    //     location.href=returl;
                    // }else{
                    //     execQQStockJSBridge(function(){
                    //         window.StockJSBridge.invoke('exit',{});
                    //     });
                    // }
                };

                Controller.setTitle = function(params) {
                    $("title").html(params.title);
                    execQQStockJSBridge(function() {
                        window.StockJSBridge.invoke("settitle", {
                            title: params.title,
                        });
                    });
                };
                Controller.setTitle({ title: document.title });

                Controller.goBackPage = function() {
                    //腾讯自选股在仅剩余一个页面时，无法返回。
                    history.go(-1);
                    window.setTimeout(function() {
                        Controller.closeAllPage();
                    }, 200);
                };

                function execQQStockJSBridge(fn) {
                    if (typeof window.StockJSBridge === "undefined") {
                        if (document.addEventListener) {
                            document.addEventListener(
                                "StockJSBridgeReady",
                                fn,
                                false
                            );
                        } else if (document.attachEvent) {
                            document.attachEvent("StockJSBridgeReady", fn);
                            document.attachEvent("onStockJSBridgeReady", fn);
                        }
                    } else {
                        // 安卓开发反馈客户端注入的时间可能比js执行的早
                        // 或者sdk在调用之前已经初始化好了
                        fn();
                    }
                }
            }
            function resetControllerForZFB() {
                Controller.setTitle = function(params) {
                    Controller.zfbReady(function () {
                        AlipayJSBridge.call('setTitle', {title: params.title});
                    });
                };
                Controller.setTitle({ title: document.title });
                Controller.goBackPage = function() {
                    Controller.zfbReady(function () {
                        AlipayJSBridge.call('popWindow');
                    });
                    // if(history.length == 1) {
                    //     Controller.zfbReady(function () {
                    //         AlipayJSBridge.call('popWindow');
                    //     });
                    // } else {
                    //     window.history.go(-1)
                    // }
                };
            }
        }
    })();
} catch (e) {
    console.log(e);
}
