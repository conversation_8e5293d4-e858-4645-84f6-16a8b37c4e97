<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,target-densitydpi = medium-dpi,viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-touch-fullscreen" content="YES">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <!--在webpack配置中配置标题 plugins-->
    <title><%= htmlWebpackPlugin.options.title%></title>
    <!--清除浏览器样式css-->
    <link href="../../common/styles/base-min.css" rel="stylesheet" />
    <!--基本样式css-设计规范 框线\背景-->
    <link href="../common/css/theme/default.css" rel="stylesheet" />
    <style>
        html,
        body {
            --sat: env(safe-area-inset-top);
            --sab: env(safe-area-inset-bottom);
        }
    </style>
    <!--换肤逻辑处理-->
    <!--通过cookie及get的皮肤参数,动态加载css对应的样式表  default.css->skinA.css -->
</head>
<body ontouchstart="">
    <script src="../../common/vendors/jquery.min.js"></script>
    <script src="../../common/scripts/common-full.js"></script>
    <script src="../../common/scripts/jssdk.js"></script>
    <script src="../../common/scripts/bridge/controller.js"></script>
    <script src="../../common/scripts/bridge/unlogin_app.js"></script>
    <script src="../common/js/yjbjyd2Title.js"></script>

    <div id="content" style="padding-bottom: 10px;"></div>


    <!--js框架引入-->
    <script src="../../common/vendors/react16.8.6/react.min.js"></script>
    <script src="../../common/vendors/react16.8.6/react-dom.min.js"></script>

    <!--<script src="../common/vendors/fastclick.js"></script>-->

    <!--&lt;!&ndash;项目基础前端配置&ndash;&gt;-->
    <script src="../common/js/loadTool.js"></script>
    <script>
        document.write('<script src="../con-f/paramConfig.clearpwd.js?v='+Math.random()+'" type="text/javascript"\><\/script>');
    </script>

    <!--基础工具方法及适配器 sdk引入-->
    <script src="../common/js/Dialog.js"></script>
    <script src="../../common/vendors/swiper.min.js"></script>

    <!-- 调试工具 -->
    <!--<script src="../../common/scripts/vconsole.min.js"></script>-->
    <!--<script src="../build/groupAPI.js"></script>-->

    <script>
        $(function(){
            document.body
            //var vConsole = new VConsole();
        });
    </script>
</body>
</html>