/**
 * Created by yancy on 2017/10/28.
 */
import React, { Component } from 'react'
//import Events from '../../common/vendors/Events.js'
import Module from 'Module'
import HandleData from 'HandleData';
import Tool from 'Tool';
export default class BaseComponent extends Component {

    constructor(props){
        super(props);
    }

    /**
     * 页面初始化
     */
    appInit = (callback,config) =>{
        Tool.waiting.show();
        let isCheckToken = false;
        /**
         * 获取当前业务页面是否需要校验token
         */
        let urlItem=window.location.pathname.split("/");
        if($.inArray(urlItem[urlItem.length-1],settings.checkLoginPage)>-1){
            isCheckToken=true;
        }
        /**
         * 进行webapp参数初始化 (动态请求接口,有等待延迟,需组件渲染后调用)
         */
        let this_=this;
        // const callbackClone = (arg)=>{
        //     callback(arg);
        // }
        App.appInit((appResult)=>{
            console.log(appResult);

            this_.loginCheckUp(appResult,(result)=>{
                try{
                    let tips = result.result.is_normal_fund_account == false ? '当前为信用交易登陆状态，请使用普通交易登陆。':'当前为期权交易登陆状态，请使用普通交易登陆。'
                    if(result.result.is_normal_fund_account==false || result.result.isNotOptionAccount==false){
                        console.log('使用信用资金账号生成token')
                        if(Controller.getAppId()=='yjb3.0'){
                            Controller.onAppear(function () {                                    
                                setTimeout(function(){
                                    Tool.waiting.hide();
                                    Dialog.showAlert(tips,'确定',function(){
                                        Controller.goBackPage();
                                    });
                                },1000);//3.0登录超时
                            });                

                            let loginType=0;//普通交易登陆
                            let regxParam = /&instant_token(\=[^&]*)?(?=&|$)|^instant_token(\=[^&]*)?(&|$)/g;
                            let backUrl=window.location.href.replace(regxParam,'');

                            //
                            backUrl = backUrl.replace('stibQueryForWeb','creditStibQueryForWeb');//科创板容错逻辑

                            //let ssoLoginUrl ='';
                            let version = '';//默认2.0
                            console.dir(backUrl);        
                            Controller.login(loginType,backUrl,'',version);
                            return;    
                        }else{
                            Dialog.notice(tips);
                            Tool.waiting.hide();
                            setTimeout(function(){Controller.closeAllPage('',3);},3000);//3.0登录超时
                            return;
                        }              
                    }    
                }catch(e){}

                //权限校验通过
                if(result&&result.code === 0) {
                    /**
                     * loadSession
                     */
                    //result.result.ifShowCredit=0//是否有信用 0-无 1-有
                    //result.result.isOrganClient=0;//是否为机构户 0-是机构户 1-非机构户 

                    let fetchRet = Tool.loadData();
                    fetchRet.then(function (oData) {
                        //Tool.waiting.hide();
                        /**
                         * 获取当前页面的groupId 及 moduleId
                         */
                        this_.props.gId=comm.TOOLS.GetQueryString('gId');
                        this_.props.mId=comm.TOOLS.GetQueryString('mId');

                        if(this_.props.gId){
                            let groupConf = HandleData.getGroupConf(this_.props.gId);
                            let moduleConf = HandleData.getModuleById(this_.props.mId,groupConf);

                            ////TODO
                            //result.result = {
                            //    fundAccount:'********',
                            //    creditFundAccount:'1********'
                            //};

                            if(moduleConf.result){
                                //有结果数据
                                let callbackData = $.extend(true,{},{
                                    initMessage : result.result, moduleData : moduleConf, groupData :groupConf
                                });
                                if(!moduleConf.setting.isCache){
                                    //TODO 清除缓存数据
                                    HandleData.handleModuleData(groupConf, false, 'noChange', function (data) {
                                        let fetchRet = Tool.saveData();
                                        fetchRet.then(function (oData) {
                                            callback(this_.loadingHide(callbackData));
                                        });
                                        callback    });
                                }else{
                                    callback(this_.loadingHide(callbackData));
                                }
                            }else{
                                //无结果数据重新请求
                                for(var m in Module){
                                    if(m==moduleConf.name){
                                        Module[m](groupConf,function(groupConf){
                                            moduleConf = HandleData.getModuleById(this_.props.mId,groupConf);
                                            let callbackData = $.extend(true,{},{
                                                initMessage : result.result, moduleData : moduleConf, groupData :groupConf
                                            });
                                            if(!moduleConf.setting.isCache){
                                                HandleData.handleModuleData(groupConf, false, 'noChange', function (data) {
                                                    let fetchRet = Tool.saveData();
                                                    fetchRet.then(function (oData) {
                                                        callback(this_.loadingHide(callbackData));
                                                    });
                                                });
                                            }else{
                                                callback(this_.loadingHide(callbackData));
                                            }
                                        });
                                    }
                                }
                            }
                        }else{
                            console.log('callback======',callback);
                            callback(this_.loadingHide({initMessage : result.result}));
                        }
                    }).catch(function (e) {
                        Tool.waiting.hide();
                        console.log(e);
                    });

                }else{
                    var mocLoginType = comm.TOOLS.GetQueryString("moclogintype");
                    Tool.waiting.hide();
                    if(config && config.ignoreError==true){
                        //忽略报错的情况,用于首页可能以未登录状态执行该方法
                        callback(this_.loadingHide({initMessage : result}));
                        return;
                    }
                    if(result===false){
                        Dialog.notice("亲，网络连接断开啦！请检查网络设置。");
                    }else{
                        if(result && result.code == 2020048){
                            // 特殊处理 code 2020048 的情况
                            Dialog.showAlert(
                                "请登录主资金账户进行业务办理，如有问题，您可尝试找回资金账号或联系服务人员咨询。",
                                "重新登录",
                                () => {
                                    // 点击重新登录按钮，跳转到登录页面
                                    let loginType = 0; // 普通交易登录
                                    let regxParam = /&instant_token(\=[^&]*)?(?=&|$)|^instant_token(\=[^&]*)?(&|$)/g;
                                    let backUrl = window.location.href.replace(regxParam,'');
                                    let version = ''; // 默认2.0
                                    Controller.login(loginType, backUrl, '', version);
                                }
                            );
                        }else if(mocLoginType === null || typeof mocLoginType === 'undefined'){
                            Dialog.notice("登录超时请返回重新登录！");
                        }
                    }

                    let ua = navigator.userAgent.toLocaleUpperCase(); // 如果鸿蒙系统 
                    if(Controller.getAppId() == 'yjb3.0' || (ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1)){
                        
                        if(mocLoginType){
                            this_.goToLogin(mocLoginType);
                        }else{
                            setTimeout(function(){Controller.closeAllPage('',3);},3000);//3.0登录超时
                        }
                    }else{
                        setTimeout(function(){Controller.closeAllPage('',3);},3000);//yjbsa的跳转地址后期再补充
                    }
                    return false;
                }
            });
        },isCheckToken);
    };

    goToLogin= (mocLoginType)=>{
        let loginType =  mocLoginType === '0' ? 0 : 1;//普通交易登陆
        let regxParam = /&instant_token(\=[^&]*)?(?=&|$)|^instant_token(\=[^&]*)?(&|$)/g;
        let backUrl=window.location.href.replace(regxParam,'');

        let version = '';//默认2.0
        console.dir(backUrl);        
        Controller.login(loginType,backUrl,'',version);
    }

    /**
     * 条件检查函数
     */
    loginCheckUp= (result,callback) =>{

        //result.result.loginCheckUp = {
        //    conclusion: 0,
        //    check_items: [
        //        {
        //            item_name: "mainFundaccount",
        //            item_status: "0"
        //        },
        //        {
        //            item_name: "enEntrustWay7",
        //            item_status: "1"
        //        }
        //    ]
        //};
        try{
            if(result.result.loginCheckUp.conclusion==1){
                //条件检查通过
                callback(result);
            }else{
                var check_items = result.result.loginCheckUp.check_items;
                for(var key in check_items){
                    if(check_items[key].item_status=='0'||check_items[key].item_status=='99'){
                        //不通过
                        if(check_items[key].item_name=='mainFundaccount'){
                            Controller.showAlert("亲，您当前登录的账号无法进行业务办理，请切换为您的主资金账号登录后再来试试。");
                            break;
                        }else if(check_items[key].item_name=='enEntrustWay7'){
                            Controller.showAlert("亲，您当前登录的账号无法进行业务办理（委托方式原因），请联系客服。");
                            break;
                        }else{
                            Controller.showAlert("亲，您当前登录的账号无法进行业务办理，请联系客服。");
                            break;
                        }
                    }
                }
                this_.loadingHide({});
                //条件检查未通过
                //解析未通过原因并提示
                setTimeout(function(){
                    //Controller.closeAllPage('',98,{backUrl:'/me/view/index.html'});
                    Controller.closeAllPage('',3);
                },3000);
                return false;

            }
        }catch(e){
            callback(result);
        }
    }

    appInitWithoutLogin = (callback) =>{

        Tool.waiting.show();

        let this_=this;
        /**
         * loadSession
         */
        let fetchRet = Tool.loadData();
        fetchRet.then(function (oData) {
            /**
             * 获取当前页面的groupId 及 moduleId
             */
            this_.props.gId=comm.TOOLS.GetQueryString('gId');
            this_.props.mId=comm.TOOLS.GetQueryString('mId');

            if(this_.props.gId){
                let groupConf = HandleData.getGroupConf(this_.props.gId);
                let moduleConf = HandleData.getModuleById(this_.props.mId,groupConf);

                ////TODO
                //result.result = {
                //    fundAccount:'********',
                //    creditFundAccount:'1********'
                //};

                if(moduleConf.result){
                    let callbackData = $.extend(true,{},{
                        moduleData : moduleConf, groupData :groupConf
                    });
                    //有结果数据
                    if(!moduleConf.setting.isCache){
                        //TODO 清除缓存数据
                        HandleData.handleModuleData(groupConf, false, 'noChange', function (data) {
                            let fetchRet = Tool.saveData();
                            fetchRet.then(function (oData) {
                                callback(this_.loadingHide(callbackData));
                            });
                        });
                    }else{
                        callback(this_.loadingHide(callbackData));
                    }
                }else{
                    //无结果数据重新请求
                    for(var m in Module){
                        if(m==moduleConf.name){
                            //Tool.waiting.show();
                            Module[m](groupConf,function(groupConf){
                                //Tool.waiting.hide();
                                moduleConf = HandleData.getModuleById(this_.props.mId,groupConf);
                                let callbackData = $.extend(true,{},{
                                    moduleData : moduleConf, groupData :groupConf
                                });
                                if(!moduleConf.setting.isCache){
                                    HandleData.handleModuleData(groupConf, false, 'noChange', function (data) {
                                        let fetchRet = Tool.saveData();
                                        fetchRet.then(function (oData) {
                                            callback(this_.loadingHide(callbackData));
                                        });
                                    });
                                }else{
                                    callback(this_.loadingHide(callbackData));
                                }
                            });
                        }
                    }
                }
            }else{
                callback(this_.loadingHide({}));
            }
        });

    };

    loadingHide = (data) =>{
        Tool.waiting.hide();
        return data;
    }
}
