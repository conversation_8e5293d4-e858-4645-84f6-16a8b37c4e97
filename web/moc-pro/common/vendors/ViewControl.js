/**
 * Created by yancy on 17/9/18.
 */
import Events from '../../common/vendors/Events.js';
import HandleData from '../../common/vendors/HandleData.js';
import Tool from '../../common/vendors/Tool.js';
import DoubleRecord_Tool from '../../src/doubleRecordView/tool/DoubleRecord_Tool.js';
import Reserve_Tool from '../../src/reserveView/components/Reserve_Tool.js';
//1 , 调用View渲染组件
class ViewControl {

    buildUrl = (groupConf, url, callback, openParam) => {

        let currentModule = HandleData.getCurrentModule(groupConf);
        url = comm.TOOLS.addUrlPara(url, 'gId', groupConf.id);
        url = comm.TOOLS.addUrlPara(url, 'mId', currentModule.id);
        url = comm.TOOLS.addUrlPara(url, 'title', $('title').html());
        /**
         * 存储SESSION变量
         */
        let fetchRet = Tool.saveData();

        fetchRet.then(function (oData) {
            //关闭当前view实例,既打开新页面需隐藏当前页面
            let index = HandleData.getModuleIndex(groupConf);
            let beforeModule = groupConf.modules[index - 1];
            if (beforeModule && beforeModule.hide) {
                Controller.pageInit(url,undefined,undefined,undefined,openParam);
            } else if (beforeModule && beforeModule.openNewPage) {
                Controller.newPageInit(url,undefined,undefined,openParam);
            } else {
                /**
                 * 通过配置读取跳转方式
                 */
                if (currentModule.setting.isOpenNewPage) {
                    Controller.newPageInit(url,undefined,undefined,openParam);
                } else {
                    Controller.pageInit(url,undefined,undefined,undefined,openParam);
                }
            }
            callback();
        });
    };

    // 前往权限开通回访问卷
    businessReturnVisitModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "businessReturnVisitView.html", callbackData);
    };

    //公告视图
    noticeModule = (groupConf, callbackData) => {
        //console.log("公告执行结果:"+JSON.stringify(groupConf));

        //跳转至公告视图
        this.buildUrl(groupConf, "noticeView.html", callbackData);
        //Controller.pageInit(this.buildUrl(groupConf,"noticeView.html"));

        /**
         * 回调参数作用: 在需要生成sso等复杂处理逻辑时需要,可回调异常场景,供View层展示
         */
        //callbackData(groupConf);
    };
    //公告组视图
    noticeGroupModule = (groupConf, callbackData) => {
        //console.log("公告执行结果:"+JSON.stringify(groupConf));

        //跳转至公告视图
        this.buildUrl(groupConf, "noticeView.html", callbackData);
        //Controller.pageInit(this.buildUrl(groupConf,"noticeView.html"));

        /**
         * 回调参数作用: 在需要生成sso等复杂处理逻辑时需要,可回调异常场景,供View层展示
         */
        //callbackData(groupConf);
    };
    //条件检查展示视图
    conditionCheckModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "conditionCheck.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("conditionCheck.html",'gId',groupConf.id));

    };
    //补充条件检查
    additionalConditionCheckModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "additionalConditionCheck.html", callbackData);
    };

    //适当性展示视图
    suitablyModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "suitablyView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("suitablyView.html",'gId',groupConf.id));
        callbackData();
    };
    
    prefSuitablyModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "suitablyView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("suitablyView.html",'gId',groupConf.id));
        callbackData();
    };

    //签署协议展示视图
    signPactModule = (groupConf, callbackData) => {
        //console.log("签署协议展示视图 :" +JSON.stringify(groupConf));
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "signPactView.html", callbackData);
        callbackData();
    };

    //风险测评展示视图
    riskModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));
        // WebApp 框架 执行跳转
        var currentModule = HandleData.getCurrentModule(groupConf);
        /**
         * 通过配置读取跳转方式
         */
        let from = comm.TOOLS.GetQueryString("from") || ''; // 跳转来源
        let backUrl = encodeURIComponent(comm.TOOLS.GetQueryString("backUrl") || ''); // 回调地址
        if (currentModule.setting.isOpenNewPage) {
             Controller.newPageInit("moc/risk/risk.html?is_sa=1&from="+from+'&backUrl='+backUrl, 2);
        } else {
            Controller.pageInit("moc/risk/risk.html?is_sa=1&from="+from+'&backUrl='+backUrl, 2);
        }
        callbackData();
    };

    //风险测评结果视图
    riskResultModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        let t = new Date().getTime();
        // WebApp 框架 执行跳转
        var currentModule = HandleData.getCurrentModule(groupConf);
        /**
         * 通过配置读取跳转方式
         */
        let from = comm.TOOLS.GetQueryString("from") || ''; // 跳转来源
        let backUrl = comm.TOOLS.GetQueryString("backUrl") || ''; // 回调地址
        if (currentModule.setting.isOpenNewPage) {
            Controller.newPageInit("moc/risk/riskresult.html?kind=1&is_sa=1&from="+from+'&backUrl='+backUrl, 2);
        } else {
            Controller.pageInit("moc/risk/riskresult.html?kind=1&is_sa=1&from="+from+'&backUrl='+backUrl, 2);
        }
        callbackData();
    };

    //港股通展示视图
    ggtQueryModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "ggtQueryView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("ggtQueryView.html",'gId',groupConf.id));

    };

    //问卷展示视图
    quesListModule = (groupConf, callbackData) => {

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "quesListView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("ggtQuesListView.html",'gId',groupConf.id));

    };

    //港股通开通\注销 结果展示视图
    ggtRetModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "ggtRetView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("ggtRetView.html",'gId',groupConf.id));

    };


    //创业板转签展示视图
    startUpBoardQueryModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "startUpBoardView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("startUpBoardView.html",'gId',groupConf.id));

    };

    //创业面转签结果视图
    startUpBoardUpdateModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "startUpBoardRetView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("startUpBoardRetView.html",'gId',groupConf.id));

    };

    //退市整理视图
    deListQueryModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "deListQueryView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("deListQueryView.html",'gId',groupConf.id));

    };

    //退市整理结果视图
    deListRetModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "deListRetView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("deListRetView.html",'gId',groupConf.id));

    };

    //风险警示展示视图
    riskwarnModule = (groupConf, callbackData) => {
        //console.log("风险警示展示视图 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "riskwarnView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("riskwarnView.html",'gId',groupConf.id));

    };

    //风险警示签署协议展示视图
    riskwarnSignpactModule = (groupConf, callbackData) => {
        //console.log("风险警示签署协议展示视图 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "riskwarnSignpactView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("riskwarnSignpactView.html",'gId',groupConf.id));

    };
    //执行结果展示页
    riskwarnResultModule = (groupConf, callbackData) => {
        //console.log("执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "riskwarnResultView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("resultView.html",'gId',groupConf.id));
        callbackData(123);
    };
    //指定交易状态查询
    specifiedModule = (groupConf, callbackData) => {
        //console.log("指定交易状态查询 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "specifiedView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("specifiedView.html",'gId',groupConf.id));

    };
    //指定交易结果展示页
    specifiedResultModule = (groupConf, callbackData) => {
        //console.log("执行结果 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "specifiedResultView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("resultView.html",'gId',groupConf.id));

    };

    //信用A股状态查询
    creditAModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditAView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("creditAView.html",'gId',groupConf.id));
    };
    //信用A股补开结果
    creditAResultModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditAResultView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("creditAView.html",'gId',groupConf.id));
    };
    //A股补开户--查询展示
    stockAModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockAView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("stockAView.html",'gId',groupConf.id));
    };
    //A股补开户-开通操作
    stockAOpenModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockAOpenView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("stockAOpenView.html",'gId',groupConf.id));
    };
    //A股补开户-开通结果
    stockARetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockARetView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("stockARetView.html",'gId',groupConf.id));
    };
    //A股补开户-申请结果
    stockAApplyRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockAApplyRetView.html", callbackData);
    };
    //信用调额展示
    creditLimitModulue = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditLimitView.html", callbackData);
        //Controller.newPageInit(comm.TOOLS.addUrlPara("creditLimitView.html",'gId',groupConf.id));
    };
    //信用调额--(调额|征信|不能调额）展示
    creditLimitAdjustModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditLimitAdjustView.html", callbackData);
        //Controller.newPageInit(comm.TOOLS.addUrlPara("creditLimitAdjustView.html",'gId',groupConf.id));
    };
    //信用调额--调额并展示结果
    creditLimitRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditLimitRetView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("creditLimitRetView.html",'gId',groupConf.id));
    };
    //信用调额--深度征信并展示结果
    //移除深度征信流程
    // creditLimitInvestModule = (groupConf, callbackData) => {
    //     this.buildUrl(groupConf, "creditLimitInvestView.html", callbackData);
    //     //Controller.newPageInit(comm.TOOLS.addUrlPara("creditLimitInvestView.html",'gId',groupConf.id));
    // };
    //问卷列表
    wjdcListModule = (groupConf, callbackData) => {
        if (settings.WJConfig.length > 1) {
            this.buildUrl(groupConf, "wjdcListView.html", callbackData);
        } else {
            window.Events.click('wjdc', '', function (data) { });
        }
    };
    //具体问卷
    wjdcModule = (groupConf, callbackData) => {
        if (groupConf['params'] && groupConf['params']['url']) {
            var url = groupConf['params']['url'];
            var kind = groupConf['params']['kind'] || 'customvisit';
            try {
                Tool.Loggercollect({ event_id: settings.loggerId[kind], logtype: kind, frompage: 'wjdcList' }); //行为采集
            } catch (e) { }
        } else {
            var url = settings.WJConfig[0]['url'];
            try {
                Tool.Loggercollect({ event_id: settings.loggerId['custom_visit'], logtype: 'custom_visit', frompage: 'wjdcList' }); //行为采集
            } catch (e) { }
        }



        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));

        url = encodeURIComponent(url);

        Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url)
        callbackData();
    };
    
    //建议与投诉
    wjdcFeedBackModule = (groupConf, callbackData) => {
        var url=settings.feedbackUrl;
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));

        url = encodeURIComponent(url);

        let currentModule = HandleData.getCurrentModule(groupConf);
        let openNewPage=true;
        openNewPage=currentModule.setting.isOpenNewPage;
        debugger
        if(openNewPage){
            Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
        }else{
            Controller.basePageInit(ajaxSite + 'api/gosite?url=' + url);
        }    
        callbackData();
    };
    //身份更新展示视图
    idupdateModule = (groupConf, callbackData) => {
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        var url = settings.IDUPDATEURL;
        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);  //这里还要加上account_type
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
        url = encodeURIComponent(url);

        //Controller.basePageInit(ajaxSite+'api/gosite?url='+url);
        Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
        callbackData()
    };
    //身份更新展示视图 --web
    idupdatewebModule = (groupConf, callbackData) => {
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        var url = settings.IDUPDATEURL;
        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);  //这里还要加上account_type
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));

        if(sessionStorage.getItem('channel_type')=='*************'){
            let conditionUrl=window.location.toString().split("moc-pro/build/")[0]+'moc-pro/build/goGroupView4qq.html?groupName=idupdateItem&backUrl=';
            conditionUrl=encodeURIComponent(conditionUrl);
            conditionUrl=encodeURIComponent(conditionUrl);//todo   
            url = comm.TOOLS.addUrlPara(url, 'conditionUrl', conditionUrl);//返回地址，适配腾讯重定向
        }
        url = encodeURIComponent(url);

        Controller.basePageInit(ajaxSite + 'api/gosite?url=' + url);
        callbackData()
    };
    //找回资金账号
    findAccountModule = (groupConf, callbackData) => {
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        var url = FINDACCOUNTURL;
        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);  //这里还要加上account_type
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
        //url= encodeURIComponent(url);
        Controller.baseNewPageInit(url);
        callbackData()
    };
    //开通融资融券
    openCreditModule = (groupConf, callbackData) => {
        let app_id = Controller.getAppId()
        let channel_type =  comm.TOOLS.getCookie('channel_type') || ''
        let openCreditUrl = settings.openCreditUrl
        let openCreditUrlJDJR = settings.openCreditUrlJDJR
        if(app_id == 'yjbweb' && channel_type == '*************'){
            Controller.baseNewPageInit(openCreditUrl)
        }else if (app_id == 'yjbweb' && channel_type == '*************') {
            Controller.baseNewPageInit(openCreditUrlJDJR)
        } else{
            this.buildUrl(groupConf, "openCreditView.html", callbackData);
        }
    };
    //开通融资融券提示页面
    openCreditHintModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "openCreditHintView.html", callbackData);
    };
    //深交所密码列表
    sztradepwdListModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "sztradepwdListView.html", callbackData);
    };
    //深交所密码申领密码
    szpwdApplyModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "szpwdApplyView.html", callbackData);
    };
    //深交所密码激活密码
    szpwdActiveModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "szpwdActiveView.html", callbackData);
    };
    //深交所密码挂失密码
    szpwdReportModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "szpwdReportView.html", callbackData);
    };
    //个人资料
    personalModule = (groupConf, callbackData) => {
        //console.log("港股通展示执行结果 :" +JSON.stringify(groupConf));

        let t = new Date().getTime();
        // this.buildUrl(groupConf, "reserveListView.html?t="+t
        // WebApp 框架 执行跳转
        var currentModule = HandleData.getCurrentModule(groupConf);
        /**
         * 通过配置读取跳转方式
         */
        if (currentModule.setting.isOpenNewPage) {
            Controller.newPageInit("moc/personal/index.html?t="+t, 2);
        } else {
            Controller.pageInit("moc/personal/index.html?t="+t, 2);
        }
        callbackData();
    };
    //个人资料 用户个人资料与中登不一致
    personalForCsdcExitModule = (groupConf, callbackData) => {
        let currentModule = HandleData.getCurrentModule(groupConf);
        let currentModuleResult = currentModule.result;
        let urlSearchStr = ''

        if (currentModuleResult && Object.keys(currentModuleResult).length) {
            urlSearchStr = '?' + Object.keys(currentModuleResult).map(key => `${key}=${currentModuleResult[key]}`).join('&')
        }

        if (currentModule.setting.isOpenNewPage) {
            Controller.newPageInit(`moc/personal/index.html${urlSearchStr}`, 2);
        } else {
            Controller.pageInit(`moc/personal/index.html${urlSearchStr}`, 2);
        }
        callbackData();
    };

    //三方存管
    thirdpartModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "thirdpartView.html", callbackData);
    };
    //信用三方存管
    creditthirdModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "thirdpartView.html", callbackData);
    };

    //三方存管变更---验证手机号
    thirdCheckModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "thirdCheckView.html", callbackData);
    };
    //三方存管变更--解卡和绑卡
    thirdOperModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "thirdOperView.html", callbackData);
    };
    //三方存管变更--绑卡
    thirdBindModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "thirdBindView.html", callbackData);
    };
    //三方存管变更--结果
    thirdRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "thirdRetView.html", callbackData);
    };

    //新开三方--选择银行
    chooseBankModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "chooseBankView.html", callbackData);
    };
    //新开三方--新开结果
    newCardRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "newCardRetView.html", callbackData);
    };
    //绑卡预指定三方--完善信息
    finishCardModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "finishCardView.html", callbackData);
    };
    //绑卡预指定三方--绑卡结果
    bindCardRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "bindCardRetView.html", callbackData);
    };

    //开放式基金账户
    openfundModule = (groupConf, callbackData) => {
        //console.log("开放式基金账户 :" +JSON.stringify(groupConf));

        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "openfundView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("bankDepositoryView.html",'gId',groupConf.id));

    };

    //在线客服
    onlineServiceModule = (groupConf, callbackData) => {

        let currentModule = HandleData.getCurrentModule(groupConf);
        let openNewPage=true;
        openNewPage=currentModule.setting.isOpenNewPage;
        
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');
        var url = settings.onlineServiceUrl;
        // if(Controller.getAppId()=='yjb3.0'){
        //     //交易端走bridge页-onlineservice支持pid在线
        //     let backUrl=url;
        //     jssdk.ready(function () {
        //         jssdk.open({
        //             leftType: 1,
        //             animated: 0,
        //             url:'/bridge/build/bridge.html?login_type=' 
        //                     + '1' //普通交易登录
        //                     + '&back_url=' 
        //                     + encodeURIComponent(backUrl) 
        //                     + '&from_url=' + encodeURIComponent(location.href)
        //                     +'&is_weak_login='+'1'
        //         },!openNewPage)
        //     });
        // }else{
            url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
            url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);  
            //url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
            url = encodeURIComponent(url);
            if(openNewPage){
                Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
            }else{
                Controller.basePageInit(ajaxSite + 'api/gosite?url=' + url);
            }    
        // }
        //Controller.baseNewPageInit(settings.onlineServiceUrl);
        callbackData();
    };
    //查找营业部
    findDepartmentModule = (groupConf, callbackData) => {
        Controller.baseNewPageInit(settings.findDepartmentUrl);
        callbackData();
    };
    /*
    //银证转账
    transferModule = (groupConf,callbackData) => {
        Controller.baseNewPageInit(settings.transferUrl);
        callbackData();
    };*/

    //即将上线
    comingSoonModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "comingSoonView.html", callbackData);
    };
    //提示
    simpleTipModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "simpleTipView.html", callbackData);
    };
    //提示
    simpleHintModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "simpleHintView.html", callbackData);
    };

    //用于外部站点跳转流程衔接
    goSiteModule = (groupConf, callbackData) => {
        let ua = navigator.userAgent.toLocaleUpperCase();
        let isHarmony = ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1;
        let param = HandleData.getGroupParam(groupConf.id);
        let url = decodeURIComponent(param.backUrl);

        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        url = comm.TOOLS.addUrlPara(url, 'app_id', isHarmony ? 'yjb3.0' : app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));


        if (param.needToken) {
            url = encodeURIComponent(url);
            Controller.locationReplace(ajaxSite + 'api/gosite?url=' + url)
            // window.location.replace(ajaxSite + 'api/gosite?url=' + url)
        } else {
            Controller.locationReplace(url)
            // window.location.replace(url)
        }
        callbackData();
    };
    //银证转账
    bankTransferV2Module = (groupConf, callbackData) => {
        try {
            var business_type = groupConf.param.business_type;
            Tool.Loggercollect({
                event_id: settings.loggerId['goto_bankTransfer'],
                logtype: 'goto_bankTransfer',
                business_type: business_type,
            }); //行为采集
        } catch (e) { }
        Controller.baseNewPageInit(settings.bankTransferV2Url);
        callbackData();
    };
    bankTransferV3Module = (groupConf, callbackData) => {
        //Controller.baseNewPageInit(settings.bankTransferV3Url);
        //callbackData();
        try {
            var business_type = groupConf.param.business_type;
            Tool.Loggercollect({
                event_id: settings.loggerId['goto_bankTransfer'],
                logtype: 'goto_bankTransfer',
                business_type: business_type,
            }); //行为采集
        } catch (e) { }

        let gotoFrontEndPage = function () {
            let app_id = Controller.getAppId();
            let op_station = comm.TOOLS.getCookie('op_station');

            var url = settings.bankTransferV3Url;
            url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
            url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);
            url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
            url = comm.TOOLS.addUrlPara(url, 'account_type', 0);  //account_type 0:普通 1:信用

            if (url.indexOf('http') < 0) {
                Controller.baseNewPageInit(url);//hybrid
                callbackData();
                return;
            }
            url = encodeURIComponent(url);
            Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
            callbackData();
        }
        let gotoNativePage = function () {
            jssdk.clientJump({
                type: 20,//native 银证转账
                tradeType: 1//普通交易银证转账
            })
            callbackData();
        }
        jssdk.ready(function () {
            jssdk.getAppVersion(function (version) {
                try {
                    if (version) {
                        //成功获取到version
                        var isiOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
                        if (isiOS && comm.TOOLS.compareVersions.compare(version, '5.02.002', '>=')) {
                            gotoNativePage();
                        } else if (comm.TOOLS.isAndroid() && comm.TOOLS.compareVersions.compare(version, '5.02.002', '>=')) {
                            gotoNativePage();
                        } else {
                            gotoFrontEndPage();
                        }
                    } else {
                        gotoFrontEndPage();
                    }
                } catch (e) {
                    //gotoFrontEndPage();
                }
            });
        });

    };
    creditBankTransferV2Module = (groupConf, callbackData) => {
        try {
            var business_type = groupConf.param.business_type;
            Tool.Loggercollect({
                event_id: settings.loggerId['goto_creditBankTransfer'],
                logtype: 'goto_creditBankTransfer',
                business_type: business_type,
            }); //行为采集
        } catch (e) { }
        Controller.baseNewPageInit(settings.creditBankTransferV2Url);
        callbackData();
    };
    creditBankTransferV3Module = (groupConf, callbackData) => {
        //Controller.baseNewPageInit(settings.bankTransferV3Url);
        //callbackData();
        try {
            var business_type = groupConf.param.business_type;
            Tool.Loggercollect({
                event_id: settings.loggerId['goto_creditBankTransfer'],
                logtype: 'goto_creditBankTransfer',
                business_type: business_type,
            }); //行为采集
        } catch (e) { }

        let gotoFrontEndPage = function () {
            let app_id = Controller.getAppId();
            let op_station = comm.TOOLS.getCookie('op_station');

            var url = settings.bankTransferV3Url;
            url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
            url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);
            url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
            url = comm.TOOLS.addUrlPara(url, 'account_type', 1);  //account_type 0:普通 1:信用
            url = comm.TOOLS.addUrlPara(url, 'auth_lv', 3);
            if (url.indexOf('http') < 0) {
                Controller.baseNewPageInit(url);//hybrid
                callbackData();
                return;
            }

            url = encodeURIComponent(url);

            Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
            callbackData();
        }
        let gotoNativePage = function () {
            jssdk.clientJump({
                type: 20,//native 银证转账
                tradeType: 2//信用交易银证转账
            })
            callbackData();
        }
        jssdk.ready(function () {
            jssdk.getAppVersion(function (version) {
                try {
                    if (version) {
                        //成功获取到version
                        var isiOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
                        if (isiOS && comm.TOOLS.compareVersions.compare(version, '5.02.002', '>=')) {
                            gotoNativePage();
                        } else if (comm.TOOLS.isAndroid() && comm.TOOLS.compareVersions.compare(version, '5.02.002', '>=')) {
                            gotoNativePage();
                        } else {
                            gotoFrontEndPage();
                        }
                    } else {
                        gotoFrontEndPage();
                    }
                } catch (e) {
                    //gotoFrontEndPage();
                }
            });
        });

    };

    //现金理财
    cashV2Module = (groupConf, callbackData) => {
        try {
            var business_type = groupConf.param.business_type;
            Tool.Loggercollect({
                event_id: settings.loggerId['goto_cash'],
                logtype: 'goto_cash',
                business_type: business_type,
            }); //行为采集
        } catch (e) { }

        //TODO 跳转至2.0交易端现金理财界面
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        var url = settings.cashV2Url;
        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);  //这里还要加上account_type
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
        url = encodeURIComponent(url);

        Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
        callbackData();
    };
    cashV3Module = (groupConf, callbackData) => {
        try {
            var business_type = groupConf.param.business_type;
            Tool.Loggercollect({
                event_id: settings.loggerId['goto_cash'],
                logtype: 'goto_cash',
                business_type: business_type,
            }); //行为采集
        } catch (e) { }

        //TODO 跳转至3.0交易端现金理财界面
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        var url = settings.cashV3Url;
        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);  //这里还要加上account_type
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
        url = encodeURIComponent(url);

        Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
        callbackData();
    };

    //修改密码
    resetpwdModule = (groupConf, callbackData) => {
        try {
            Tool.Loggercollect({ event_id: settings.loggerId['resetpassword'], logtype: 'resetpassword' }); //行为采集
        } catch (e) { }
        Controller.baseNewPageInit(settings.resetpwdUrl);
        callbackData();
    };
    //修改交易密码
    resetTradePwdModule = (groupConf, callbackData) => {
        try {
            Tool.Loggercollect({ event_id: settings.loggerId['resetTradePwd'], logtype: 'resetTradePwd' }); //行为采集
        } catch (e) { }
        Events.click('resetTradePwd_sd', '', function (data) { });
        // Controller.baseNewPageInit(settings.resetTradePwdUrl);
        callbackData();
    };
    //修改资金密码
    resetFundPwdModule = (groupConf, callbackData) => {
        try {
            Tool.Loggercollect({ event_id: settings.loggerId['resetFundPwd'], logtype: 'resetFundPwd' }); //行为采集
        } catch (e) { }
        Events.click('resetFundPwd_sd', '', function (data) { });
        // Controller.baseNewPageInit(settings.resetFundPwdUrl);
        callbackData();
    };


    ////CDR与创新企业 start
    cdrQueryModule = (groupConf,callbackData) => {
       // WebApp 框架 执行跳转
       this.buildUrl(groupConf,"cdrQueryView.html",callbackData);
       //Controller.pageInit(comm.TOOLS.addUrlPara("deListQueryView.html",'gId',groupConf.id));
    };
    
    cdrRetModule = (groupConf,callbackData) => {
       // WebApp 框架 执行跳转
       this.buildUrl(groupConf,"cdrRetView.html",callbackData);
       //Controller.pageInit(comm.TOOLS.addUrlPara("deListRetView.html",'gId',groupConf.id));
    };
    
    creditCdrQueryModule = (groupConf,callbackData) => {
       this.buildUrl(groupConf,"creditCdrQueryView.html",callbackData);
    };
    creditCdrRetModule = (groupConf,callbackData) => {
       this.buildUrl(groupConf,"creditCdrRetView.html",callbackData);
    };
    innovationStockQueryModule = (groupConf,callbackData) => {
       this.buildUrl(groupConf,"innovationStockQueryView.html",callbackData);
    };
    innovationStockRetModule = (groupConf,callbackData) => {
       this.buildUrl(groupConf,"innovationStockRetView.html",callbackData);
    };
    creditInnovationStockQueryModule = (groupConf,callbackData) => {
       this.buildUrl(groupConf,"creditInnovationStockQueryView.html",callbackData);
    };
    creditInnovationStockRetModule = (groupConf,callbackData) => {
       this.buildUrl(groupConf,"creditInnovationStockRetView.html",callbackData);
    };


    //CDR与创新企业 end

    //开户
    openStockModule = (groupConf, callbackData) => {
        let openStockUrl;
        if (Controller.getAppId() == 'yjb3.0') {
            //yjb开户
            //考虑修改为bridge 方式
            openStockUrl = settings.openStockUrl['yjb3.0'];
        } else if (Controller.getAppId() == 'yjbwx') {
            //微信开户
            openStockUrl = settings.openStockUrl['yjbwx'];
        } else if (Controller.getAppId() == 'yjbweb') {
            //todo yjbweb
            //web版开户
            //部分渠道可能需要修改为渠道开户的地址
            openStockUrl = settings.openStockUrl['yjbweb'];
        } else if (Controller.getAppId() == 'yjbjyd') {
            //2.0跳转到开户app下载页面
            // if(comm.TOOLS.getTerminalTypeFromUserAgent()==2){
            //    //安卓
            //     openStockUrl =settings.open_app_download_link['android'];
            //}else if(comm.TOOLS.getTerminalTypeFromUserAgent()==1){
            //    //IOS
            //     openStockUrl =settings.open_app_download_link['ios'];
            //}

            var u = navigator.userAgent;
            var url;
            if (u.indexOf('Android') > -1 || u.indexOf('Linux') > -1) {//安卓手机
                url = "appurl=" + encodeURIComponent("cn.com.gjzq.yjb.kh.forothers://?PreFix=QBA&SkinType=1") + "&&downloadurl=" + encodeURIComponent(settings.open_app_download_link['android']);
            } else if (u.indexOf('iPhone') > -1) {//苹果手机
                url = "appurl=" + encodeURIComponent("com.yjb.kh://ui=1&terminal=tzt&callid=gjtrade.gjkaihu") + "&&downloadurl=" + encodeURIComponent(settings.open_app_download_link['ios']);
            } else if (u.indexOf('Windows Phone') > -1) {//winphone手机
                return;
            }
            Controller.changeURL("http://action:10073/?" + url);
            callbackData();
            return;
        }
        var ua = navigator.userAgent.toLocaleUpperCase();
        if(ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1) {
            openStockUrl = settings.openStockUrl['yjb3.0'];
        }
        Controller.baseNewPageInit(openStockUrl);
        callbackData();
    };


    //债权逆回购查询
    reverseReposQueryModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "reverseReposQueryView.html", callbackData);
        //Controller.pageInit(comm.TOOLS.addUrlPara("deListQueryView.html",'gId',groupConf.id));
    };
    reverseReposRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "reverseReposRetView.html", callbackData);
    };

    //找回资金账号


    goFindAccountModule = (groupConf, callbackData) => {
        var url = settings.findaccount_presonal;
        let app_id = Controller.getAppId() || comm.TOOLS.GetQueryString('app_id');
        let op_station = comm.TOOLS.getCookie('op_station') || comm.TOOLS.GetQueryString('op_station');
        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station); //这里还要加上account_type
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
        url = comm.TOOLS.addUrlPara(url, 'frompage', 'moc');

        let currentModule = HandleData.getCurrentModule(groupConf);
        let openNewPage=true;
        openNewPage=currentModule.setting.isOpenNewPage;
        if(openNewPage){
            Controller.baseNewPageInit(url);
        }else{
            Controller.basePageInit(url);
        } 
        callbackData();
    };

    //重置密码
    goClearPwdModule = (groupConf, callbackData) => {
        Events.click('goClearPwd_sd', '', function (data) { });
        // var url = settings.clearpassword;
        // let app_id = Controller.getAppId() || comm.TOOLS.GetQueryString('app_id');
        // let op_station = comm.TOOLS.getCookie('op_station') || comm.TOOLS.GetQueryString('op_station');
        // url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        // url = comm.TOOLS.addUrlPara(url, 'op_station', op_station); //这里还要加上account_type
        // url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
        // url = comm.TOOLS.addUrlPara(url, 'frompage', 'moc');
        // let currentModule = HandleData.getCurrentModule(groupConf);
        // let openNewPage=true;
        // openNewPage=currentModule.setting.isOpenNewPage;
        // if(openNewPage){
        //     Controller.baseNewPageInit(url);
        // }else{
        //     Controller.basePageInit(url);
        // } 
        callbackData();
    };
    //沪伦通
    londonCdrQueryModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "londonCdrQueryView.html", callbackData);
    };
    londonCdrRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "londonCdrRetView.html", callbackData);
    };
    //预约业务
    reservationListModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "reservationListView.html", callbackData);
    };
    phoneVerifyModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "phoneVerifyView.html", callbackData);
    };
    //预约转销户
    accountCancellationModule = (groupConf, callbackData) => {

    };
    accountCancellationApplyRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "accountCancellationApplyRetView.html", callbackData);
    };
    accountCancellationQueryModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "accountCancellationQueryView.html", callbackData);
    };
    accountCancellationQuestionnaireModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "accountCancellationQuestionnaireView.html", callbackData);
    };
    accountCancellationUploadIdModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "accountCancellationUploadIdView.html", callbackData);
    };
    accountCancellationUploadSupplementModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "accountCancellationUploadSupplementView.html", callbackData);
    };
    accountCancellationVideoModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "accountCancellationVideoView.html", callbackData,{keepActive:1});
    };
    accountCancellationInfoModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "accountCancellationInfoView.html", callbackData);
    };
    doubleRecordListModule = (groupConf, callbackData) => {
        let t = new Date().getTime();
        this.buildUrl(groupConf, "doubleRecordListView.html?t="+t, callbackData);
    };
    doubleRecordHistoryModule = (groupConf, callbackData) => {
        let t = new Date().getTime();
        this.buildUrl(groupConf, "doubleRecordHistoryView.html?t="+t, callbackData);
    };
    doubleRecordRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "doubleRecordRetView.html", callbackData);
    };    
    investorListModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "investorListView.html", callbackData);
    };
    investorIntroModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "investorIntroView.html", callbackData);
    };
    investorPhoneVerifyModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "phoneVerifyInvestorView.html", callbackData);
    };
    investorUploadPicModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "investorUploadPicView.html", callbackData);
    };
    investorContractModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "investorContractView.html", callbackData);
    };
    investorRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "investorRetView.html", callbackData);
    };

    userInfoConfirmModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "userInfoConfirmView.html", callbackData);
    };

    //佣金宝APP
    yjbAppModule = (groupConf, callbackData) => {
        let appUrl;
        if (Controller.getAppId() == 'yjbwx') {
            //微信开户
            appUrl = settings.app_download_link['wx'];
        } else if (comm.TOOLS.isAndroid()) {
            appUrl = settings.app_download_link['android'];
        } else {
            appUrl = settings.app_download_link['ios'];
        }
        Controller.baseNewPageInit(appUrl);
        callbackData();
    };
    //科创板
    stibQueryModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "stibQueryView.html", callbackData);
    };
    stibRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stibRetView.html", callbackData);
    };
    //北交所
    stockBeiQueryModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "stockBeiQueryView.html", callbackData);
    };
    stockBeiAdditionConditionModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockBeiAdditionConditionView.html", callbackData);
    };
    stockBeiOpenRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockBeiRetView.html", callbackData);
    };
    stockBeiCloseRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockBeiCloseRetView.html", callbackData);
    };
    //新债
    debentureQueryModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "debentureQueryView.html", callbackData);
    };
    ptxzManageOpenRetModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "debentureRetView.html", callbackData);
    };
    debentureCloseRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "debentureCloseRetView.html", callbackData);
    };
    //信用新债
    creditDebentureQueryModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "creditDebentureQueryView.html", callbackData);
    };
    inputCreditInfoModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "inputCreditInfoView.html", callbackData);
    };
    xyxzManageOpenRetModule = (groupConf, callbackData) => {
        // WebApp 框架 执行跳转
        this.buildUrl(groupConf, "creditDebentureRetView.html", callbackData);
    };
    creditDebentureCloseRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditDebentureCloseRetView.html", callbackData);
    };
    //信用科创板
    creditStibQueryModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStibQueryView.html", callbackData);
    };
    creditStibRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStibRetView.html", callbackData);
    };

    fundSignUpModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "fundSignUpView.html", callbackData);
    };
    //封闭式基金账户
    closedEndFundModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "closedEndFundView.html", callbackData);
    };
    closedEndFundOpenModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "closedEndFundOpenView.html", callbackData);
    };
    closedEndFundRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "closedEndFundRetView.html", callbackData);
    };
    contractQueryModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "contractQueryView.html", callbackData);
    };
    contractInfoModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "contractInfoView.html", callbackData);
    };
    contractFileViewModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "contractFileView.html", callbackData);
    };    
    contractFileThirdViewModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "contractFileThirdView.html", callbackData);
    };    
    contractContentModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "contractPureContentView.html", callbackData);
    };
    contractInfoThirdModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "contractInfoThirdView.html", callbackData);
    };

    /*预约业务/特殊视频业务*/
    reserveListModule = (groupConf, callbackData) => {
        let t = new Date().getTime();
        this.buildUrl(groupConf, "reserveListView.html?t="+t, callbackData);
    };
    reserveUploadIdCardModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "reserveUploadIdCardView.html", callbackData);
    };
    reserveUploadSupplementCardModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "reserveUploadSupplementCardView.html", callbackData);
    };
    reserveUploadHeadimgModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "reserveUploadHeadimgView.html", callbackData);
    };
    normalUploadIdCardModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "normalUploadIdCardView.html", callbackData);
    };
    normalUploadSupplementCardModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "normalUploadSupplementCardView.html", callbackData);
    };
    idInfoEditModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "idInfoEditView.html", callbackData);
    };
    reserveRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "reserveRetView.html", callbackData);
    };
    reserveVideoWitnessModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "reserveVideoWitnessView.html", callbackData,{keepActive:1});
    };


    preHandlerForWebModule = (groupConf, callbackData) => {
    };

    highFinancialModule = (groupConf, callbackData) => {
        let currentModule = HandleData.getCurrentModule(groupConf);
        let openNewPage=true;
        openNewPage=currentModule.setting.isOpenNewPage;
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        var url = settings.highFinancial;

        // if(app_id=='yjb3.0'){
        //     let nativeLoginType=1;
        //     let backUrl=url;
        //     let version = '';//默认2.0
        //     jssdk.ready(function(){
        //         jssdk.login(nativeLoginType,backUrl,version,'',1);
        //     });
        //     //Controller.login(loginType,backUrl,'',version);
        // }else{
        url = comm.TOOLS.addUrlPara(url, 'account_type', '0');
        url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
        url = comm.TOOLS.addUrlPara(url, 'op_station', op_station); 
        url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
        url = encodeURIComponent(url);

        if(openNewPage){
            Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
        }else{
            Controller.basePageInit(ajaxSite + 'api/gosite?url=' + url);
        }  
        callbackData();   
        // }      
    };
    //股票期权
    optionAccountModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "optionAccountView.html", callbackData);
    };
    optionConfirmPermissionModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "optionConfirmPermissionView.html", callbackData);
    };
    
    optionAccountHintModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "optionAccountHintView.html", callbackData);
    };
    optionAccountSHHintModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "optionAccountSHHintView.html", callbackData);
    };
    optionAccountConditionCheckModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "optionAccountConditionCheckView.html", callbackData);
    };
    optionRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "optionRetView.html", callbackData);
    };


    //新三板交易权限
    stockRotationQueryModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockRotationQueryView.html", callbackData);
    };
    stockRotationChooseTypeModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockRotationChooseTypeView.html", callbackData);
    };
    stockRotationCloseRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "stockRotationCloseRetView.html", callbackData);
    };

    thirdBoardOpenRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "thirdBoardOpenRetView.html", callbackData);
    };

    //签约标准咨询服务
    iadviserBasicQueryModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "iadviserBasicQueryView.html", callbackData);
    };
    counselingSignAgreementModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "counselingSignAgreementView.html", callbackData);
    };
    counselingResultModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "counselingResultView.html", callbackData);
    };
    
    counselingConfirmationModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "counselingConfirmationView.html", callbackData);
    };

    /**
     * 创业板开通
     */
    startUpBoardV2QueryModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "startUpBoardV2View.html", callbackData);
    };

    //创业面转签结果视图
    startUpBoardV2OpenRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "startUpBoardV2RetView.html", callbackData);

    };

    startUpBoardZqOpenRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "startUpBoardZqRetView.html", callbackData);
    };
    startUpBoardBqOpenRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "startUpBoardBqRetView.html", callbackData);
    };

    creditStartUpBoardModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStartUpBoardView.html", callbackData);
    };
    //信用创业板补签告知书
    creditStartUpBqConfirmModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStartUpBqConfirmView.html", callbackData);
    };


    creditStartUpBoardOpenRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStartUpBoardOpenRetView.html", callbackData);
    };

    // //财人汇-专业投资者
    // professionalInvestorModule = (groupConf, callbackData) => {
    //     let app_id = Controller.getAppId();
    //     let op_station = comm.TOOLS.getCookie('op_station');
    //     let typeName ='professionalInvestor';
    //     console.log(app_id,op_station)

    //     Tool.waiting.hide();

    //     let currentModule = HandleData.getCurrentModule(groupConf);
    //     let openNewPage=currentModule.setting.isOpenNewPage;

    //     if(openNewPage){
    //         Controller.baseNewPageInit(ajaxSite + 'api/gocrh?pageType=' + typeName
    //         +'&app_id='+app_id
    //         +'&op_station='+op_station);
    //     }else{
    //         Controller.pageInit(ajaxSite + 'api/gocrh?pageType=' + typeName
    //         +'&app_id='+app_id
    //         +'&op_station='+op_station);
    //     }    
    //     // callbackData()
    // };

    // //财人汇-可转债
    // holderRightsModule = (groupConf, callbackData) => {
    //     let app_id = Controller.getAppId();
    //     let op_station = comm.TOOLS.getCookie('op_station');
    //     let typeName ='holderRights';
    //     console.log(app_id,op_station)

    //     Tool.waiting.hide();

    //     let currentModule = HandleData.getCurrentModule(groupConf);
    //     let openNewPage=currentModule.setting.isOpenNewPage;

    //     if(openNewPage){
    //         Controller.baseNewPageInit(ajaxSite + 'api/gocrh?pageType=' + typeName
    //         +'&app_id='+app_id
    //         +'&op_station='+op_station);
    //     }else{
    //         Controller.basePageInit(ajaxSite + 'api/gocrh?pageType=' + typeName
    //         +'&app_id='+app_id
    //         +'&op_station='+op_station);
    //     }    
    //     // callbackData()
    // };
    //跳转财人汇功能模块
    goCRHModule = (groupConf, callbackData) => {
        let app_id = Controller.getAppId();
        let op_station = comm.TOOLS.getCookie('op_station');

        let currentModule = HandleData.getCurrentModule(groupConf);
        let openNewPage=currentModule.setting.isOpenNewPage;

        /**
         * typeName
         * holderRights 可转债
         * professionalInvestor 专业投资者
         */
        let typeName =currentModule.param.pageType;

        // console.log(app_id,op_station)
        Tool.waiting.hide();

        let targetUrl=ajaxSite + 'api/gocrh?pageType=' + typeName
        +'&app_id='+app_id
        +'&op_station='+encodeURIComponent(op_station);

        let conditionUrl=window.location.toString().split("moc-pro/build/")[0]+'moc-pro/build/goGroupView4crh.html';
        const search = new URLSearchParams(window.location.search);
        conditionUrl=encodeURIComponent(conditionUrl);
        targetUrl = comm.TOOLS.addUrlPara(targetUrl, 'conditionUrl', conditionUrl);//返回地址，适配腾讯重定向

        if(search.get("channelBackUrl")) { // 返回地址,适配京东金融等
            targetUrl = comm.TOOLS.addUrlPara(targetUrl, 'channelBackUrl', encodeURIComponent(search.get("channelBackUrl")));
        }
        if(sessionStorage.getItem('channel_type')
        &&sessionStorage.getItem('channel_type')!='null'){
            targetUrl = comm.TOOLS.addUrlPara(targetUrl, 'channel_type', sessionStorage.getItem('channel_type'));
        }

        /**
         * 服务拆分临时适配逻辑
         * 特殊入参切换至另一台服务器
         */
        if(currentModule.param.hostType=='second'){
            targetUrl = comm.TOOLS.addUrlPara(targetUrl, 'hostType', 'second');
        }

        if(openNewPage){
            Controller.baseNewPageInit(targetUrl);
        }else{
            Controller.basePageInit(targetUrl);
        }    
        // callbackData()
    };

    //跳转思迪功能模块
    goSdModule = (groupConf) => {
        Tool.waiting.hide();
        let domain = '';
        if (['devwebapps.yjbtest.com', 'localhost'].some(_ => location.host.indexOf(_) > -1)) {
            domain = 'fzsdbusinesscdn.yjbtest.com';
        } else if (location.host.indexOf('fzwebapps.yjbtest.com') > -1) {
            // domain = 'uatfzsdbusiness.yjbtest.com:8443';
            if(location.pathname.indexOf('yjbwebmoc2') > -1){ // uat2 环境
                domain = 'uat2fzsdbusinesscdn.yjbtest.com';
            }else if(location.pathname.indexOf('yjbwebmoc3') > -1){ // uat3 环境
                domain = 'uat3fzsdbusiness.yjbtest.com';
            }else if(localStorage.getItem('isSdSitEnv') === '1') { // sit 环境
                domain = 'fzsdbusinesscdn.yjbtest.com';
            }else {
                domain = 'uatfzsdbusinesscdn.yjbtest.com';  // 默认全部走uat
            }
        } else if(location.host.indexOf('prewebapps.yongjinbao.com.cn') > -1) {
            domain= 'preacctbizcdn.yongjinbao.com.cn';
        }else if(location.host.indexOf('spwebapps.yongjinbao.com.cn') > -1) {
            domain= 'spacctbizcdn.yongjinbao.com.cn';
        } else if (location.host.indexOf('webapps.yongjinbao.com.cn') > -1) {
            domain = 'acctbizcdn.yongjinbao.com.cn';
        }
        let from = comm.TOOLS.GetQueryString("from") || ''; // 跳转来源
        let backUrl = encodeURIComponent(comm.TOOLS.GetQueryString("backUrl") || ''); // 回调地址
        let wzq_id = encodeURIComponent(comm.TOOLS.GetQueryString("wzq_id") || ''); // 腾讯的用户id
        let channel_type = comm.TOOLS.getCookie('channel_type') || ''; // 渠道编号
        let op_station = comm.TOOLS.getCookie('op_station') || 'yjbapi';
        let currentModule = HandleData.getCurrentModule(groupConf);
        let openNewPage= currentModule.setting.isOpenNewPage;
        console.log('currentModule', groupConf, currentModule);
        if(groupConf.param && groupConf.param.openInit) {
            openNewPage= false;
        }
        let bizType = currentModule.param.bizType || ''; // 思迪业务编号
        let subBizType = currentModule.param.subBizType || ''; // 思迪业务子编号
        let optType = currentModule.param.optType || ''; // 1-登录，2-权限开通，3-其它
        let specialLink = currentModule.param.specialLink || ''; // 是否存在介绍页
        let needLogin = groupConf.needLogin || ''; // 是否需要登录
        let app_id = Controller.getAppId();
        if(app_id == 'yjb3.0' || app_id == 'yjbjyd') {
            app_id = 'yjb3.0';
        } else if (app_id == 'yjbwx') {
            app_id = 'yjbwx';
        } else if (app_id == 'yjbweb' && channel_type == '*************') {
            app_id = 'yjbths';
        } else if (app_id == 'yjbweb' && channel_type == '*************') { // 自选股  
            app_id = 'yjbzxg';
        } else if (app_id == 'yjbweb' && channel_type == '*************') { // 微证券
            app_id = 'yjbwzq';
        } else {
            app_id = 'yjbweb';
        }
        if(bizType == '010013' && from == 'login') {
            optType = '1'
        }
        let account_type = comm.TOOLS.GetQueryString("account_type") || '';
        let sd_token = comm.TOOLS.getCookie('sd_token');
        let isHarmonyWeb= '';
        var ua = navigator.userAgent.toLocaleUpperCase();
        if(ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1 &&  location.href.indexOf('goGroupView') === -1) {
            // 业务办理首页菜单入口跳转时走h5的逻辑
            isHarmonyWeb= 'h5';
        }
        let url = 
        `https://${domain}/bc-h5-view/views/${specialLink ? specialLink: ''}?bizType=${bizType}&subBizType=${subBizType}&optType=${optType}&backUrl=${backUrl}&from=${from}&app_id=${app_id}&channel_type=${channel_type}&op_station=${encodeURIComponent(op_station)}&tk_moduleName=ygt&account_type=${account_type}&isHarmonyWeb=${isHarmonyWeb}&wzq_id=${wzq_id}`;
        if(sd_token && channel_type=='*************') {
            url = `${url}&instant_token=no_token`;
            comm.TOOLS.clearCookie('cgjzq_sd_token'); // 跳转前清除cookie
            Controller.toThinkivePage(url, openNewPage);
        } else {
            let uri = ajaxSite.split('phpservice/moc')[0]+'phpservice/moc/api'
            console.log('uri',uri)
            // 如果不需要登录的不用发这个请求直接跳转
            if(needLogin === '0'){
                comm.TOOLS.clearCookie('cgjzq_sd_token'); // 跳转前清除cookie
                Tool.Loggercollect({
                    event_id: ********,
                    logtype: 'goSd',
                    message: url
                }); 
                Controller.toThinkivePage(url, openNewPage);
                return
            }
            Tool.fetchTo({account_type},{},['2410003', '880'],uri +"/gettoken?").then((data) => {
                if (data.code == 0 && data.result && data.result.instantToken) {
                    const instant_token = data.result.instantToken;
                    url = `${url}&instant_token=${instant_token}`;
                    comm.TOOLS.clearCookie('cgjzq_sd_token'); // 跳转前清除cookie
                    Tool.Loggercollect({
                        event_id: ********,
                        logtype: 'goSd',
                        message: encodeURIComponent(url)
                    }); 
                    Controller.toThinkivePage(url, openNewPage);
                } else if(data.code == '2410003') {
                    // durable_token令牌过期，令牌在首次进入业务办理页面生成，有效期依赖生成instant_token设置的时间，默认为半小时
                    Dialog.notice("登录超时请返回重新登录！");
                    setTimeout(function(){Controller.closeAllPage('',3);},3000);
                } else if(data.code == '880') {
                    // 兼容无需登录的入口
                    comm.TOOLS.clearCookie('cgjzq_sd_token'); // 跳转前清除cookie
                    Tool.Loggercollect({
                        event_id: 20240111,
                        logtype: 'goSd',
                        message: encodeURIComponent(url)
                    }); 
                    Controller.toThinkivePage(url, openNewPage);
                } else {
                    Controller.showAlert('系统异常，请稍后重试！' ,'确定', function(){});
                }
            })
        }
    }

    // 跳转思迪时客户端版本校验
    checkSdVersionModule= (groupConf, callbackData)=> {
        let from = comm.TOOLS.GetQueryString("from") || ''; // 跳转来源
        let account_type = comm.TOOLS.GetQueryString("account_type") || ''; // 登录类型
        let currentModule = HandleData.getCurrentModule(groupConf);
        this.buildUrl(groupConf, "checkSdVersionView.html?from="+from+"&account_type="+account_type, callbackData);
    }

    // 五层四级调价，佣金、融资利率调整
    priceAdjustConfirmModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "priceAdjustConfirmView.html", callbackData);
    };

    // 专项头寸权限-查询
    specialSecuritiesQueryModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "specialSecuritiesQueryView.html", callbackData);
    };

    // 专项头寸权限-开通结果
    specialSecuritiesRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "specialSecuritiesRetView.html", callbackData);
    };

    // 专项头寸权限-注销结果
    specialSecuritiesCancelRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "specialSecuritiesRetView.html?is_cancel=1", callbackData);
    };

    //理财商城，场外基金转场内
    mallFundTransferModule = (groupConf, callbackData) => {
        let currentModule = HandleData.getCurrentModule(groupConf);
        let openNewPage=true;
        openNewPage=currentModule.setting.isOpenNewPage;
        Tool.waiting.hide();
        if(Controller.getAppId()=='yjb3.0'){
            //交易端
            var url=settings.mallFundTransfer;
            let backUrl=url;
            jssdk.ready(function () {
                jssdk.open({
                    leftType: 1,
                    animated: 0,
                    url:'/bridge/build/bridge.html?login_type=' 
                            + '1' 
                            + '&back_url=' 
                            + encodeURIComponent(backUrl) 
                            + '&from_url=' + encodeURIComponent(location.href)
                            +'&is_weak_login='+'1'
                },!openNewPage)
            });

        }else{
            var url=settings.mallFundTransfer;

            let app_id = Controller.getAppId();
            let op_station = comm.TOOLS.getCookie('op_station');
    
            url = comm.TOOLS.addUrlPara(url, 'app_id', app_id);
            url = comm.TOOLS.addUrlPara(url, 'op_station', op_station);
            if(sessionStorage.getItem('channel_type') && sessionStorage.getItem('channel_type')!='null'){
                url = comm.TOOLS.addUrlPara(url, 'channel_type', sessionStorage.getItem('channel_type'));
            }
    
            url = encodeURIComponent(url);
    
    
            if(openNewPage){
                Controller.baseNewPageInit(ajaxSite + 'api/gosite?url=' + url);
            }else{
                Controller.basePageInit(ajaxSite + 'api/gosite?url=' + url);
            }    
            callbackData();
    
        }

    };

    //活体检测
    liveDetectionModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "liveDetectionView.html", callbackData);
    };

    // 税收居民身份信息
    taxInfoModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "taxInfoView.html", callbackData);
    };
    //修改交易密码
    changeTradePasswordModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "changeTradePasswordView.html", callbackData);
    };
    //修改资金密码
    changeFundPasswordModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "changeFundPasswordView.html", callbackData);
    };

    // 关联关系确认
    associationConfirmModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "associationConfirmView.html", callbackData);
    };

    // 关联关系确认
    associationListModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "associationListView.html", callbackData);
    };

    // 无持仓证明
    positionProveModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "positionProveView.html", callbackData);
    };

    // 关联关系确认结果
    associationConfirmResultModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "associationConfirmResultView.html", callbackData);
    };
    
    // 业务办理进度查询
    searchProgressModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "searchProgress.html", callbackData);
    };

    // 双元对账单
    checkAccountModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "checkAccountView.html", callbackData);
    };
    
    checkAccountResultModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "checkAccountResultView.html", callbackData);
    };

    // 信用北交所列表
    creditStockBeiQueryModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStockBeiQueryView.html", callbackData);
    };

    // 信用北交所特转A列表
    creditStockBeiSpecialModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStockBeiSpecialView.html", callbackData);
    };

    // 信用北交所董监高信息收集
    creditStockBeiInfoCollectModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStockBeiInfoCollectView.html", callbackData);
    };

     // 信用北交所——开通结果
     creditStockBeiOpenRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStockBeiOpenRetView.html", callbackData);
    };

    // 信用北交所——投教音频
    creditStockBeiAudioModule  = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "creditStockBeiAudioView.html", callbackData);
    };
    // 可转债退市整理权限
    delistHolderRightsModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "delistHolderRightsView.html", callbackData);
    };
    // 可转债退市整理权限 开通结果页
    delistHolderRightsRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "delistHolderRightsRetView.html", callbackData);
    };
    // 可转债退市整理权限  取消
    delistHolderRightsCancelModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "delistHolderRightsCancelView.html", callbackData);
    };
    // 可转债退市整理权限  取消结果页
    delistHolderRightsCancelRetModule = (groupConf, callbackData) => {
        this.buildUrl(groupConf, "delistHolderRightsCancelRetView.html", callbackData);
    };
    // 指定产品购买双录
    // 指定产品购买双录
    doubleRecordModule = (_groupConf, _callbackData) => {
        let doubleRecordId = comm.TOOLS.GetQueryString("double_record_id"); // 跳转来源
        let dualVideoProductName = comm.TOOLS.GetQueryString("dualVideoProductName"); // 回调地址
        DoubleRecord_Tool.createPreCommonBusinessInfo(doubleRecordId, dualVideoProductName)
			.then(res => {
				let param = {
                    UniqueKey: 'RESERVE_03',
                    todoId: res.todoId,
                }
                Tool.fetchTo(param,{},'all').then(async (data) => {
                    if (data.code == 0) {
                        let todoInfo = await Reserve_Tool.queryReserveDetail(data.result.todoId);
                        Reserve_Tool.startProcess(todoInfo, { doubleRecordId: doubleRecordId });
                    }else{
                        //待办失效/已完成
                        Controller.showAlert('该双录已完成，请耐心等待办理结果。如果您尚未完成全部申请流程，请通过"我的-我的业务-产品购买双录"菜单完成后续申请。'
                            ,'确定',function(){
                                Controller.goBackPage();
                            });
                    }
                })
			})
			.catch(err => {
				console.log(err);
			});
    };
}

if (window && typeof module === 'object' && typeof module.exports === 'object') {
    module.exports = window.ViewControl = new ViewControl();
}
