import React, { Component } from 'react';
import { render } from 'react-dom';
import Button from '../components/button/Button.jsx';
import Notice from '../components/notice/Notice.jsx';
import Events from 'Events';
import BaseComponent from '../../common/vendors/ClearpwdBaseComponent.js';

import conditionCheckDict from '../components/conditionCheck/ConditionCheckDict.js';
import ConditionCheckList from '../components/conditionCheck/conditionCheckList.jsx';
import { resetConditionCheckList } from '../components/conditionCheck/resetList';
import Tool from '../../common/vendors/Tool.clearpassword';

/**
 * 重置密码-条件检查
 * */
export default class ClearpswConditionCheckView extends BaseComponent {
	constructor(props) {
		super(props);

		this.initData = this.initData.bind(this);
		this.resetList = this.resetList.bind(this);
		this.checkNotice = this.checkNotice.bind(this);
		this.checkWXNotice = this.checkWXNotice.bind(this);
		this.goNext = this.goNext.bind(this);
		this.goBack = this.goBack.bind(this);
		this.state = {
			title: '',
			content: '',
			checkList: null
		};
	}

	static defaultProps = {};

	componentWillMount() {
		try {
			if(!settings.sdBothSwitch) {
				Dialog.notice("当前网络加载异常，请稍后重试。");
				setTimeout(function(){Controller.closeAllPage('',3);},3000);
				Tool.Loggercollect({ logtype: 'getparam_error', message: location.href });
				Tool.waiting.hide();
				return;
			}
		} catch (e) {}
		if (true) {
			Controller.onAppear(function() {
				if(Controller.getAppId() == 'yjb3.0' && comm.TOOLS.isAndroid()) {
					Controller.goBackPage();
				}
			});
			this.appInit(() => {
				window.setTimeout(function() {
					Events.click('goClearPwdForWeb', null, function(callback) {}, {openInit: true});
					Tool.Loggercollect({ event_id: settings.loggerId['clearpwd_init'], logtype: 'clearpwd_init_sd' }); //行为采集
				});
			}, 10);
		} else {
			let this_ = this;
			Controller.onAppear(function() {
				//todo yjbweb
				if (Controller.getAppId() == 'yjbwx') {
					this_.checkWXNotice();
				} else {
					this_.checkNotice();
				}
			});
	
			let frompage = comm.TOOLS.GetQueryString('frompage');
			if (frompage) {
				comm.TOOLS.setCookie('frompage', frompage);
			} else {
				comm.TOOLS.clearCookie('cgjzq_frompage');
			}
	
			this.appInit(() => {
				window.setTimeout(function() {
					//防止session未初始化
					if (Controller.getAppId() == 'yjbwx') {
						this_.checkWXNotice();
					} else {
						this_.checkNotice();
					}
					try {
						Tool.Loggercollect({ event_id: settings.loggerId['clearpwd_init'], logtype: 'clearpwd_init' }); //行为采集
					} catch (e) {}
				}, 10);
			});
		}
	}

	resetList(result) {
		//result.conclusion = 0;
		if (result.conclusion == 1) {
			//todo 清密条件检查通过时，直接进入下一步0
			this.goNext();
		}
		//result.conclusion = 1; //todo
		let sortResult = resetConditionCheckList(result);
		this.setState({ checkList: sortResult });
	}

	initData() {
		let this_ = this;
		let fetchRet = Tool.fetchTo({
			UniqueKey: 'ACU_001',
			business_type: '30032', // todo 清密条件检查business_id
			op_station: '($op_station)'
		});
		fetchRet
			.catch(function(e) {
				console.log(e);
			})
			.then(function(oData) {
				console.log('fetch result' + JSON.stringify(oData));
				this_.resetList(oData.result);
			});
	}

	checkNotice() {
		let this_ = this;
		//公告检查
		let noticeRet = Tool.fetchTo(
			{
				UniqueKey: 'ANNC_1004',
				op_station: '($op_station)',
				type: '1002',
				subtype: '10020021' //todo 清密业务公告
				//subtype: '10039999'
			},
			{},
			'all'
		);
		noticeRet
			.catch(function(e) {
				console.log(e);
			})
			.then(function(oData) {
				if (oData.code === 0) {
					if (oData.result && oData.result.announceList.length > 0) {
						var serverTime = oData.result.serverTime; //服务器时间
						var announceList = oData.result.announceList;
						var notice = 0;
						for (var i = 0; i < announceList.length; i++) {
							var validFrom = new Date(announceList[i].validFrom.replace(/-/g, '/')).getTime();
							var validTo = new Date(announceList[i].validTo.replace(/-/g, '/')).getTime();
							if (serverTime <= validTo && serverTime >= validFrom && announceList[i].status == 0) {
								//展示公告
								// states- 1 : 已完成  states- 0 : 未完成
								var result = {
									title: announceList[i].title,
									content: announceList[i].content
								};
								this_.setState({
									title: result.title,
									content: result.content
								});
								try {
									Tool.Loggercollect({
										event_id: settings.loggerId['notice'],
										logtype: 'notice',
										noticeid: announceList[i]['id']
									}); //行为采集
								} catch (e) {}
								return;
								//try{
								//    Tool.Loggercollect({event_id:settings.loggerId['notice'],logtype:'notice',noticeid:announceList[i]['id']}); //行为采集
								//}catch(e){}

								break;
							} else {
								//非公告时间
								notice++;
							}
						}
						//所有的公告都不在有效期，视为无公告
						if (notice == announceList.length) {
							//
							this_.initData();
						}
					} else {
						this_.initData();
					}
				} else {
					this_.initData();
				}
			});
	}

	checkWXNotice() {
		//公告检查
		let this_ = this;

		let noticeRet = Tool.fetchTo(
			{
				UniqueKey: 'ANNC_1004',
				op_station: '($op_station)',
				type: '1002',
				subtype: '10020021' //todo 清密业务公告
				//subtype: '10039999'
			},
			{},
			'all'
		);
		noticeRet
			.catch(function(e) {
				console.log(e);
			})
			.then(function(oData) {
				if (oData.code === 0) {
					if (oData.result && oData.result.announceList.length > 0) {
						var serverTime = oData.result.serverTime; //服务器时间
						var announceList = oData.result.announceList;
						var notice = 0;
						for (var i = 0; i < announceList.length; i++) {
							var validFrom = new Date(announceList[i].validFrom.replace(/-/g, '/')).getTime();
							var validTo = new Date(announceList[i].validTo.replace(/-/g, '/')).getTime();
							if (serverTime <= validTo && serverTime >= validFrom && announceList[i].status == 0) {
								//展示公告
								// states- 1 : 已完成  states- 0 : 未完成
								var result = {
									title: announceList[i].title,
									content: announceList[i].content
								};
								this_.setState({
									title: result.title,
									content: result.content
								});
								try {
									Tool.Loggercollect({
										event_id: settings.loggerId['notice'],
										logtype: 'notice',
										noticeid: announceList[i]['id']
									}); //行为采集
								} catch (e) {}
								return;
								//try{
								//    Tool.Loggercollect({event_id:settings.loggerId['notice'],logtype:'notice',noticeid:announceList[i]['id']}); //行为采集
								//}catch(e){}

								break;
							} else {
								//非公告时间
								notice++;
							}
						}
						//所有的公告都不在有效期，视为无公告
						if (notice == announceList.length) {
							//
							this_.initData();
						}
					} else {
						this_.initData();
					}
				} else {
					this_.initData();
				}
			});
	}

	goNext() {
		console.warn('go next');
		//跳转至身份确认页面
		Controller.pageInit('verifyFundAccountView.html' + location.search);
	}

	goBack() {
		Tool.closeAllPage();
	}

	submitForm = () => {
		//Events.setGroupParam(this.props.gId,{kkk:'111'});
		console.log(123123);

		let result = {};
		let fetchRet = Tool.fetchTo({
			UniqueKey: 'ACU_001',
			business_type: '30029', //找回资金账号
			op_station: 'yjbapi'
		});
		fetchRet
			.then(function(oData) {
				console.log('fetch result' + JSON.stringify(oData));
			})
			.catch(function(e) {
				console.log(e);
			});
	};

	render() {
		let this_ = this;

		if (this.state.title != '') {
			return (
				<div>
					<div className={`'show' notice`}>
						<Notice title={this.state.title} content={this.state.content} />
					</div>
				</div>
			);
		}

		return (
			<div>
				<ConditionCheckList
					value={this.state.checkList}
					className="list-class"
					itemClassName="list-item-class"
					action={this_.goNext}
					goBackFunc={this_.goBack}
				/>
			</div>
		);
	}
}
render(<ClearpswConditionCheckView />, document.querySelector('#content'));
