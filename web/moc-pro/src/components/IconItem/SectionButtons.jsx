import React, { Component } from 'react';
import { render } from 'react-dom';
import './section.scss';

import BaseComponent from  '../../../common/vendors/BaseComponent.js'

import Events from 'Events';
import Tool from 'Tool';

import navConfig from  '../../indexView/navConfig'

export default class SectionButton extends BaseComponent {
    constructor(props) {
        super(props);

        this.clickHandler = this.clickHandler.bind(this);
        this.iconFunc = this.iconFunc.bind(this);
        this.needRender = this.needRender.bind(this);
        this.state = {}
    }

    clickHandler() {
        localStorage.setItem('type', '');
        let btnKey = this.props.value;
        let btnInfo = navConfig['buttons'][btnKey];
        let this_=this;

        console.warn('btn click')
        console.log(btnInfo);
        /**
         * todo 处理不需要登陆的业务逻辑
         */

        //todo 默认的页面跳转设置

        let defaultTransferLogic = JSON.parse(JSON.stringify(navConfig.defaultTransferLogic));//默认跳转逻辑

        let transferLogic = Object.assign(defaultTransferLogic,btnInfo.transferLogic);
        console.log("当前按钮的跳转逻辑");
        console.log(transferLogic);

        if(transferLogic.needLogin == false){
            //不要求登陆,直接跳转
            console.log("不要求登陆,直接跳转");
            this_.iconFunc(transferLogic.defaultParam);
            return;
        }





        /**
         * todo
         * 校验是否为登陆状态
         * 如果不在登陆状态 则呼出登陆界面
         *
         */
        let phpSessionId = comm.TOOLS.getCookieByName('MOCPHPSESSID');
        //if(phpSessionId ==null){
        //    //php session 无效 认为是未登陆状态
        //}
        //
        //
        //if(Controller.getAppId=='yjb3.0'){
        //
        //}else{
        //    //yjbwx和yjbjyd的处理逻辑
        //
        //}



        /**
         * todo
         * 校验SESSION内信息是否符合业务需求
         * 符合需求的进行业务办理
         * 不符合需求的，跳转到对应的提示页面
         */
        this.appInit((data)=>{
            Tool.waiting.hide();
            console.log("APP INIT RESULT");
            console.log(data);

            if(data&&data.initMessage){
                this.loginInfo=data.initMessage;
            }
            // TODO
            // data.initMessage.ifShowCredit = 1;
            if(data.initMessage==undefined || data.initMessage.clientId == undefined){
                /**
                 * 对未登陆状态的处理
                 */

                console.warn('当前未登录');
                console.log(comm.TOOLS.addUrlPara(window.location.href,'login_back',btnKey));
                let ua = navigator.userAgent.toLocaleUpperCase(); // 如果鸿蒙系统 
                if(Controller.getAppId()=='yjb3.0' || (ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1)){
                    //todo 需要区分token有效，但userinfoquery报错的情况

                    if(comm.TOOLS.getCookie('app_init_error')!=null){
                        //重复校验，会报token失效的错误，从而丢失原有的错误类型
                        data.initMessage=JSON.parse(comm.TOOLS.getCookie('app_init_error'));
                    }
                    if(data.initMessage.code!=-882 && data.initMessage.code!=-996){
                        //不是token失效，而是账户本身异常的状态
                        if(data.initMessage===false){
                            Dialog.notice("亲，网络连接断开啦！请检查网络设置。");
                        }else if(!data.initMessage.result){
                            Dialog.notice("登录超时请返回重新登录！");
                        }else if(data.initMessage.code == -990){
                            // 特殊处理 code -990 的情况
                            Dialog.showAlert(
                                "请登录主资金账户进行业务办理，如有问题，您可尝试找回资金账号或联系服务人员咨询。",
                                "重新登录",
                                () => {
                                    // 点击重新登录按钮，跳转到登录页面
                                    let loginType = 0; // 普通交易登录
                                    let backUrl = comm.TOOLS.replaceUrlParam(window.location.href,'login_back',btnKey);
                                    let version = ''; // 默认2.0
                                    Controller.login(loginType, backUrl, '', version);
                                }
                            );
                        }else{
                            Dialog.notice(data.initMessage.result);
                        }
                        comm.TOOLS.setCookie('app_init_error',JSON.stringify(data.initMessage));
                        setTimeout(function(){Controller.closeAllPage('',3);},3000);//3.0登录超时
                        return false;
                    }else{
                        //token失效的情况
                        //3.0调用登陆功能
                        let loginType=0;//普通交易登陆
                        let backUrl=comm.TOOLS.replaceUrlParam(window.location.href,'login_back',btnKey);
                        //let ssoLoginUrl ='';
                        let version = '';//默认2.0
                        //调用ukeylogin
                        //调用bridge.html


                        Controller.login(loginType,backUrl,'',version);
                    }
                }else{
                    //其他端直接弹出
                    Dialog.notice("登录超时请返回重新登录！");
                    setTimeout(()=>{
                        // 同花顺渠道--首页点击菜单时，登录信息获取异常时重新登录
                        let channel_type =  comm.TOOLS.getCookie('channel_type') || ''
                        if(Controller.getAppId() == 'yjbweb' && channel_type == '*************'){
                            let loginUrl = location.origin + '/yjbweblogin/login/web/build/index.html?app_id=yjbweb&backUrl='
                            let backUrl = location.origin + '/yjbwebmoc/moc/web/moc-pro/build/indexView.html?app_id=yjbweb&channel_type=*************&account_type=0'
                            loginUrl= loginUrl + encodeURIComponent(backUrl);
                            location.replace(loginUrl);
                        } else {
                            Controller.closeAllPage('',3);
                        }
                    },3000);//3.0登录超时
                    return false;
                }
                return;
            }
            /**
             * 当前客户已经登陆
             */
            
            let pageTitle=btnInfo.transferLogic.pageTitle||btnInfo.desc;
            let btnGroupName = transferLogic.defaultParam.groupName;
            // let goToSd = (settings.sdSwitch || []).includes(btnGroupName.split('ForWeb')[0]) || comm.TOOLS.GetQueryString("sdTag") == '1';
            // let goToSd = !['delistHolderRights', 'searchProgress'].includes(btnGroupName.split('ForWeb')[0]) || comm.TOOLS.GetQueryString("sdTag") == '1';
            let goToSd = true;
            //当前为机构户，但要求为个人户
            if(data['initMessage']['isOrganClient']==0 && transferLogic.needPersonal==true){
                //不满足个人户的情况
                console.log("不满足个人户的情况");
                window.setTimeout(function(){
                    let orgParam = Object.assign(transferLogic.orgParam,{pageTitle:pageTitle});
                    //this_.iconFunc(transferLogic.orgParam);//执行机构户逻辑
                    this_.iconFunc(orgParam);//执行机构户逻辑


                },10)

                return;
            }
            //当前无信用户，但要求有信用户
            if(data['initMessage']['ifShowCredit']!=1 && transferLogic.needCredit==true && !goToSd){
                //不满足信用账号要求的情况
                console.log("不满足信用账号要求的情况");
                window.setTimeout(function(){
                    let noCreditParam = Object.assign(transferLogic.noCreditParam,{pageTitle:pageTitle});
                    this_.iconFunc(noCreditParam);//执行没有信用账户的逻辑
                },10)
                return;
            }
            //满足所有要求，执行默认跳转方法
            //延时跳转,防止cookie中的值未更新
            console.log("执行默认的跳转方法");
            window.setTimeout(function(){
               this_.iconFunc( Object.assign(transferLogic.defaultParam,{pageTitle:pageTitle}));
            },10)

        },{ignoreError:true});

        //this.iconFunc(btn.groupName,btn.logType)
    }

    /**
     * 是否需要渲染当前按钮
     * @param renderLogic
     * @returns {boolean}
     */
    needRender(renderLogic){
        /**
         * 默认展示功能按钮
         */
        let needRender= true;
        /**
         * 如果配置了renderLogic.app_id，则在当前app_id不在列表中时不渲染对应按钮
         */
        if(renderLogic
            && renderLogic.app_id
            && $.inArray(Controller.getAppId(),renderLogic.app_id)<0){
            needRender = false;
        }
        return needRender;
    }


    iconFunc(param) {
        let {groupName, logType,pageTitle} =param
        console.log(param);
        //return;//todo test code

        try {
            Tool.Loggercollect({event_id: settings.loggerId[logType], logtype: logType, frompage: 'index'}); //行为采集
        } catch (e) {
        }

        /**
         * 初始化group click 事件
         * name: group -name
         * groupId : group -id
         * 回调函数: 回调group处理结果(异常报错等)
         */
        if(pageTitle){
            Events.click(groupName, null, function (data) {
                console.log("执行完毕");
            },{
                pageTitle:pageTitle,
                // loginInfo:this.loginInfo
            });
        }else{
            Events.click(groupName, null, function (data) {
                console.log("执行完毕");
            },{
                // loginInfo:this.loginInfo
            });
        }

    }

    render() {
        let btnKey = this.props.value;
        let btnInfo = navConfig['buttons'][btnKey];
        // let renderLogic = Object.assign({},navConfig.defaultRenderLogic,btnInfo.renderLogic)

        // if(this.needRender(renderLogic)){

        // }else{
        //     return null;
        // }


        return (
            <span onClick={this.clickHandler} className={`login_back_target_${btnKey}`}>
                {this.props.children}
            </span>
        );
    }

}