<?php
namespace moc\controllers;
use Yii;
//use yii\filters\AccessControl;
use yii\base\Exception;
use yii\web\Controller;
use common\library\LogFunc;
use common\library\SessionFunc;
use common\library\ApiFunc;
use common\library\CommonFunc;
use common\library\AESFunc;
use common\library\SsoFunc;

    class ApiController extends BaseController
{
	public $layout = false;

    //检测用户是否登录(通过instant_token验证用户是否登录等)
    //$instant_token == 'notoken'表示native已经退出登录，而xgsg的session依然是登录状态,此时必须清空session数据。
    public function actionChecklogin()
    {
        $this->startFunc();
        $instant_token = Yii::$app->request->get('instant_token');
        $op_station = Yii::$app->request->get('op_station');
        $account_type = (Yii::$app->request->get('account_type')==null) ? SessionFunc::readSession('account_type') : Yii::$app->request->get('account_type');

        $auth_lv=1;
        if($account_type==0){
            //普通
            $auth_lv=1;
            $account_type='0';
        }else if($account_type==1){
            //信用
            $auth_lv=2;
            $account_type='1';
        }else if($account_type==15){//url中的account_type
            //期权
            $auth_lv=1;
            //php中的account_type 此处依照惯例和url中的保持一致
            $account_type='15';
        }else{
            //默认普通账户
            $account_type='0';
        }

        //外围或首页点击时传递token过来时，token不能直接传‘’而要传notoken。此时如果token不存在，要去清空对应的session
        LogFunc::_log("debug",'行'.__LINE__.'【instant_token-->开始】--'.$instant_token.'--account_type--'.$account_type
            .'$op_station---'.$op_station,'page');

        if($instant_token == 'notoken' || $instant_token == 'undefined'){
            $prefix = Yii::$app->params['cookie_prefix'];

            unset($_SESSION[$prefix.'sso_message']);
            unset($_SESSION[$prefix.'sso_message_expiretime']);

            //SessionFunc::clearSession(); //勿删：改方法做调试用
            LogFunc::_log("debug",'行'.__LINE__.'---清空后的Session_cookie信息--'.$_COOKIE[session_name()],'page');
            return  json_encode(array('code'=>-882,'message'=>'notoken情况下，清空了session'));
        }else if($instant_token != ''){
            //新的instant_token,清除group流程session脏数据
            $prefix = Yii::$app->params['cookie_prefix'];
            unset($_SESSION[$prefix.'SESSION']);
            unset($_SESSION[$prefix.'backUrl']);
            unset($_SESSION[$prefix.'fromlogin']);
            unset($_SESSION[$prefix.'preSaveResult']);
            unset($_SESSION[$prefix.'userParams']);
        }

        //op_station应在存储Session变量时统一处理,方便读取  update by jyc
        $checkLoginRet = CommonFunc::checkLoginInfov2($instant_token,$account_type,$op_station,$auth_lv);
        LogFunc::_log("debug",'行'.__LINE__.'【instant_token<--结束】--'.$instant_token.'--account_type--'.$account_type.'--$checkLoginRet结果是--'.json_encode($checkLoginRet,true),'page');
        //$checkLoginRet的返回值有多种可能，涵盖了instance_token为不为空的所有情况
        if($checkLoginRet['code']!=0){
            $prefix = Yii::$app->params['cookie_prefix'];
            unset($_SESSION[$prefix.'sso_message']);
            unset($_SESSION[$prefix.'sso_message_expiretime']);
            LogFunc::_log("debug",'行'.__LINE__.'---出现异常，进行清理cookie动作--清理后的cookie'.$_COOKIE[session_name()],'page');
            return json_encode($checkLoginRet,true);
        }

        /**
         * 获取密码
         */
        $password='';
        $SsoFunc = new SsoFunc();
        $res_pwd=$SsoFunc->getPwdV2($checkLoginRet['result']['cipherToken']);
        if($res_pwd['code'] == 0){
            $password=$res_pwd['result']['cipherContent'];
        }


        if($checkLoginRet['result']['is_normal_fund_account']===null){
            //未判断是否是使用普通资金账号生成的token时,校验是否为普通资金账号
            $clientId = $checkLoginRet['result']['userinfo_client_id'];
            $currentFundAccount = $checkLoginRet['result']['account'];

        
            $params = array(
                'commonFunc'=>true,
                'act'=>'counter.web.fundaccount.query',//todo
                'op_client_id'=>rawurlencode($clientId),
            );
            $res = ApiFunc::api_curl($params, 'counter.web.fundaccount.query--行：'.__LINE__);
            $res=json_decode($res, true);

            LogFunc::_log("debug",'行'.__LINE__.'counter.web.fundaccount.query查询结果'.json_encode($res,true),'page');
            LogFunc::_log("debug",'行'.__LINE__.'token换取的资金账号：'.$currentFundAccount.'客户号：'.$clientId,'page');
            $isNormalFundAccount=true;
            $isNotOptionAccount=true;
            if($res['code'] == 0){
                //查询正确
                $accountList =$res['result']['fund_account'];
                foreach ($accountList as $k => $v) {
                    // LogFunc::_log("debug",'行'.__LINE__.'**'.$v['fund_account'].'**'.$v['asset_prop'],'debug');
                    if($v['fund_account']==$currentFundAccount && $v['asset_prop']=='7'){
                        $isNormalFundAccount=false;
                    }
                    if($v['fund_account']==$currentFundAccount && $v['asset_prop']=='B'){
                        $isNotOptionAccount=false;
                    }
                }
            }
            LogFunc::_log("debug",'行'.__LINE__.'是否为信用资金账号校验结果'.$isNormalFundAccount,'page');
            LogFunc::_log("debug",'行'.__LINE__.'是否为期权资金账号校验结果'.$isNotOptionAccount,'page');
            //重写checkLoginRet信息
            $checkLoginRet['result']['is_normal_fund_account']=$isNormalFundAccount;
            $checkLoginRet['result']['isNotOptionAccount']=$isNotOptionAccount;
            //存储至Session
            if($account_type == 0){  //普通资金账号
                $sso_session = $checkLoginRet['result'];
                SessionFunc::writeSession(array(
                    'sso_message'=>base64_encode(serialize($sso_session)),
                    'account_type'=>'0'
                ));
            } elseif ($account_type == 1){  //信用资金账号

                //该方法会覆盖掉原有的userinfoquery信息
                /*
                $sso_credit_session = $res['result'];
                SessionFunc::writeSession(array(
                    'sso_credit_message'=>base64_encode(serialize($sso_credit_session)),
                    'account_type'=>'1'
                ));*/
                try{
                    $oldObj=unserialize(base64_decode(SessionFunc::readSession('sso_credit_message')));
                    $newObj=$res['result'];
                    if($newObj){
                        $combineResult=array_merge($oldObj,$newObj);
                    }else{
                        $combineResult=$oldObj;
                    }
                    SessionFunc::writeSession(array(
                        'sso_credit_message'=>base64_encode(serialize($combineResult)),
                        'account_type'=>'1'
                    ));
                }
                catch(Exception $e){
                    $sso_credit_session = $res['result'];
                    SessionFunc::writeSession(array(
                        'sso_credit_message'=>base64_encode(serialize($sso_credit_session)),
                        'account_type'=>'1'
                    ));
                }
            }  elseif ($account_type == 15){  //期权资金账号

                try{
                    $oldObj=unserialize(base64_decode(SessionFunc::readSession('sso_option_message')));
                    $newObj=$res['result'];
                    if($newObj){
                        $combineResult=array_merge($oldObj,$newObj);
                    }else{
                        $combineResult=$oldObj;
                    }
                    SessionFunc::writeSession(array(
                        'sso_option_message'=>base64_encode(serialize($combineResult)),
                        'account_type'=>'15'
                    ));
                }
                catch(Exception $e){
                    $sso_option_session = $res['result'];
                    SessionFunc::writeSession(array(
                        'sso_option_message'=>base64_encode(serialize($sso_option_session)),
                        'account_type'=>'15'
                    ));
                }
            }                     
        }


        /**
         * 获取信用资金账号
         */

        $creditFundAccount=null;
        $ifShowCredit=null;
        //判断session中是否有存储过信用资金账号信息
        if($checkLoginRet['result']['credit_fund_account']!==null){
            //有信用资金账号
            $creditFundAccount=$checkLoginRet['result']['credit_fund_account'];
            $ifShowCredit=$checkLoginRet['result']['if_show_credit'];

        }else{
            //获取信用资金账号
            $params = array(
                'commonFunc'=>true,
                'act'=>'marginFundAccountQuery',
                'client_id'=>$checkLoginRet['result']['userinfo_client_id'],
                'op_station'=>rawurlencode($checkLoginRet['result']['op_station']),
                'password'=>$password
            );
            $res = ApiFunc::api_curl($params, 'marginFundAccountQuery--行：'.__LINE__);
            $res=json_decode($res, true);
            if($res['code'] == 0){
                $creditFundAccount=$res['result']['credit_fund_account'];
                $ifShowCredit=$res['result']['if_show_credit'];
            }

            $checkLoginRet['result']['credit_fund_account']=$creditFundAccount;
            $checkLoginRet['result']['if_show_credit']=$ifShowCredit;
            //存储至Session
            if($account_type == 0){  //普通资金账号
                $sso_session = $checkLoginRet['result'];
                SessionFunc::writeSession(array(
                    'sso_message'=>base64_encode(serialize($sso_session)),
                    'account_type'=>'0'
                ));
            } elseif ($account_type == 1){  //信用资金账号

                //该方法会覆盖掉原有的userinfoquery信息
                /*
                $sso_credit_session = $res['result'];
                SessionFunc::writeSession(array(
                    'sso_credit_message'=>base64_encode(serialize($sso_credit_session)),
                    'account_type'=>'1'
                ));*/
                try{
                    $oldObj=unserialize(base64_decode(SessionFunc::readSession('sso_credit_message')));
                    $newObj=$res['result'];
                    if($newObj){
                        $combineResult=array_merge($oldObj,$newObj);
                    }else{
                        $combineResult=$oldObj;
                    }
                    SessionFunc::writeSession(array(
                        'sso_credit_message'=>base64_encode(serialize($combineResult)),
                        'account_type'=>'1'
                    ));
                }
                catch(Exception $e){
                    $sso_credit_session = $res['result'];
                    SessionFunc::writeSession(array(
                        'sso_credit_message'=>base64_encode(serialize($sso_credit_session)),
                        'account_type'=>'1'
                    ));
                }
            } elseif ($account_type == 15){  //期权资金账号 url中的account_Type

                try{
                    $oldObj=unserialize(base64_decode(SessionFunc::readSession('sso_option_message')));
                    $newObj=$res['result'];
                    if($newObj){
                        $combineResult=array_merge($oldObj,$newObj);
                    }else{
                        $combineResult=$oldObj;
                    }
                    SessionFunc::writeSession(array(
                        'sso_option_message'=>base64_encode(serialize($combineResult)),
                        'account_type'=>'15'
                    ));
                }
                catch(Exception $e){
                    $sso_option_session = $res['result'];
                    SessionFunc::writeSession(array(
                        'sso_option_message'=>base64_encode(serialize($sso_option_session)),
                        'account_type'=>'15'
                    ));
                }
            }        
        }

        /**
         * 获取入口条件检查结果
         */
        $loginCheckUp = null;
        $params = array(
            'commonFunc'=>true,
            'act'=>'checkUp',
            'business_type' => '30001',
            'client_id'=>$checkLoginRet['result']['userinfo_client_id'],
            'fund_account' =>$checkLoginRet['result']['account'],
            'op_station'=>rawurlencode($checkLoginRet['result']['op_station']),
            'password'=>$password
        );
        $res = ApiFunc::api_curl($params, 'checkUp--行：'.__LINE__);
        $res=json_decode($res, true);
        if($res['code'] == 0){
            $loginCheckUp=$res['result'];
        }

        /**
         * 重新对返回数据封装
         */
        $checkLoginRet['result']=array(
            'clientId' => $checkLoginRet['result']['userinfo_client_id'],
            'clientType'=> $checkLoginRet['result']['userinfo_client_type'],
            'fundAccount' => $checkLoginRet['result']['account'],
            'userName' => $checkLoginRet['result']['userinfo_client_name'],
            'gender' => $checkLoginRet['result']['userinfo_gender'],
            'isOrganClient' => $checkLoginRet['result']['userinfo_is_organ_client'],
            'ifShowCredit' =>$ifShowCredit,
            'creditFundAccount' => $creditFundAccount,
            'loginCheckUp' => $loginCheckUp,
            'opStation' => rawurlencode($checkLoginRet['result']['op_station']),
            'is_normal_fund_account' => $checkLoginRet['result']['is_normal_fund_account'],
            'isNotOptionAccount' => $checkLoginRet['result']['isNotOptionAccount']
        );

        return json_encode($checkLoginRet,true);
    }

    public function actionLogin4qqstock()
    {
        $this->startFunc();
        $session_id = Yii::$app->request->post('session_id');        

        $useSign=Yii::$app->params['qqStockUseSign'];
        if($useSign){
            $singKey=Yii::$app->params['qqStockSignKey'];
            $time_stamp= time();
            $param_string='SERVICE_ID=700001'.'&TIME_STAMP='.$time_stamp.'&session_id='.$session_id.'&key='.$singKey;
            //LogFunc::_log("debug",'行'.__LINE__.'签名串--'.$param_string.'--','apis');
            $sign=(md5($param_string));
            $params=array(
                "REQUESTS"=>[
                    array(
                        "REQ_MSG_HDR"=>array(
                            "SERVICE_ID"=>"700001",
                            "TIME_STAMP"=>$time_stamp,
                            "SIGN"=>$sign
                        ),
                        "REQ_COMM_DATA"=>array("session_id"=>$session_id)
                    )
                ]
            );
        }else{
            $params=array(
                "REQUESTS"=>[array("REQ_MSG_HDR"=>array("SERVICE_ID"=>"700001"),"REQ_COMM_DATA"=>array("session_id"=>$session_id))]
            );
        }       
        //echo json_encode($params);

        $params = array(
            'act'=>'qq.kesb_req',//todo
            'params'=>$params
        );
        $res = ApiFunc::api_curl($params, 'qq.kesb_req--行：'.__LINE__);

        return $res;
    }



    public function actionGosite()
    {
        $this->startFunc();
        if(Yii::$app->request->isPost){
            $params=Yii::$app->request->post();
        }else{
            $params=Yii::$app->request->get();
        }
        $account_type = (Yii::$app->request->get('account_type')==null) ? SessionFunc::readSession('account_type') : Yii::$app->request->get('account_type');
        $url = $params['url'] ;
        //$app_id = $params['app_id'] ;
        // 打印url
        LogFunc::_log("debug",'URL日志采集--'.json_encode($url),'page');
        if(!$this->checkGoSiteURL($url) && Yii::$app->params[goSiteCheck]){

            LogFunc::_log("debug",'URL阻断日志采集--'.json_encode($url),'page');
            echo '该链接为非合法链接，为保证您的信息安全，建议不要进行访问。';  
            return header("HTTP/1.0 404 Not Found"); //通知浏览器 页面不存在
        }
        
        $op_station = $params['op_station'] ;
        $url = rawurldecode($url);
        //判断浏览器在复合url中如多做了一次decode,会解析出op_station参数
        if($op_station!=null||$op_station!=''){
            $url=$url.'&op_station='.$op_station;
        }

        $checkLoginRet = CommonFunc::checkLoginInfov2('',$account_type,"","");
        if($checkLoginRet['code'] != 0){

            return header('Refresh:0; url='.$url);
        }else{
            $retInfo = $checkLoginRet['result'];
            $durableToken=$retInfo['durableToken'];

            //获取一次性token
            $params = array(
                'commonFunc'=>true,
                'act'=>'instant_tokenApply',
                'durable_token'=>$durableToken
            );
            $res = ApiFunc::api_curl($params, 'marginFundAccountQuery--行：'.__LINE__);
            $res=json_decode($res, true);
            if($res['code'] == 0){
                $instantToken=$res['result']['instantToken'];
                LogFunc::_log("debug",'行'.__LINE__.'【go_site】--'.$url.'&instant_token='.$instantToken.'--','page');
                return header('Refresh:0; url='.$url.'&instant_token='.$instantToken);
            }else{
                return header('Refresh:0; url='.$url);
            }
        }
    }

    public function checkGoSiteURL($backUrl){
        if($backUrl==null){
            return false; // 空校验
        }
        $allow_domain_list = array(
            "/.*yjbtest.com/",
            "/.*yongjinbao.com.cn/"
        );
        $url = parse_url($backUrl);
        if($url["scheme"] !="https"){
            return false; // https 协议校验
        }
        $check = false;
        foreach($allow_domain_list as $key => $value){
            $check = preg_match($value,$url["host"]) == 1? true:$check;
        }
        return $check;
    }

    //跳转才人汇功能模块
    public function actionGocrh()
    {
        $this->startFunc();
        if(Yii::$app->request->isPost){
            $params=Yii::$app->request->post();
        }else{
            $params=Yii::$app->request->get();
        }

        $account_type = (Yii::$app->request->get('account_type')==null) ? SessionFunc::readSession('account_type') : Yii::$app->request->get('account_type');
        //根据pageType拼装url
        $pageParam=array(
            'professionalInvestor'=>'?#!/business?toUrl=yjb/investorIdentify',//专业投资者
            'holderRights'=>'?#!/business?toUrl=holderRights/600401',//可转债
            'marginTradeReserve'=>'#/?busin=600345',//两融预约
            'fundPrefrozen'=>'#/?busin=600455',//中签资金预冻结
            'quoteRepurchase'=>'#/?busin=600049',//报价回购
            'specialRights'=>'#/?busin=600166',//特转A账户开通
            'delistRights'=>'#/?busin=600156',//退市板块权限开通
            'marginRenewal'=>'#/?busin=600485',//开通两融合约展期自动申请权限
            'exerciseFinancing'=>'#/?busin=600410',//开通行权融资业务权限
            'equityIncentiveSign'=>'#/?busin=600504',//股权激励授权签署
            'crhInfrastructureFund'=>'?#!/business?toUrl=holderRights/600403',//基础设施基金
            'crhStockOptions'=>'?#!/business?toUrl=stockOptions',//期权额度
            'crhCheckAccount'=>'?#!/business?toUrl=checkAccount',//对账单
            //'crhTest'=>'?#!/business?toUrl=holderRights/600403',//TEST
            'crhFundOpenTpye'=>'?#!/business?toUrl=fund/openType',//开放式基金开户
            'crhTaRelationInfo'=>'?#!/business?toUrl=cert/relevance/600333',//TA关系维护
            'crhOpenConfirm'=>'?#!/business?toUrl=/agreement/openconfirm/600425/2',//开户确认单
            'onLineOpenStock'=>'#/?busin=600007',//两融全线上开户
        )[$params['pageType']];

        if($pageParam==null){
            //默认值
            $pageParam='#';
        }
        $url = Yii::$app->params['crh_business'].$pageParam;
        if($params['pageType'] == 'specialRights' 
        || $params['pageType'] == 'fundPrefrozen'
        || $params['pageType'] == 'marginTradeReserve'
        || $params['pageType'] == 'marginRenewal'
        || $params['pageType'] == 'onLineOpenStock'
        || $params['pageType'] == 'quoteRepurchase'
        || $params['pageType'] == 'delistRights'
        || $params['pageType'] == 'exerciseFinancing'
        || $params['pageType'] == 'equityIncentiveSign'){
            $url = Yii::$app->params['crh_business'].'wt2/'.$pageParam;
            // $url = Yii::$app->params['crh_business_kf_new'].'wt2/'.$pageParam;
        }
        if($params['hostType']=='second'){
            $url = Yii::$app->params['crh_business_second'].$pageParam;
        }
        //LogFunc::_log("debug",'行'.__LINE__.'【jump_to_crh】--'.$params['hostType'],'debug');
        //LogFunc::_log("debug",'行'.__LINE__.'【go site base url】--'.$url,'debug');

        $app_id = $params['app_id'] ;
        $url=$url.'&app_id='.$app_id;

        $op_station = $params['op_station'] ;
        if($op_station!=null||$op_station!=''){
            $url=$url.'&op_station='.$op_station;
        }
        $channel_type = $params['channel_type'] ;
        if($channel_type!=null||$channel_type!=''){
            $url=$url.'&channel_type='.$channel_type;
        }
        $conditionUrl = $params['conditionUrl'] ;
        if($conditionUrl!=null||$conditionUrl!=''){
            $url=$url.'&conditionUrl='.$conditionUrl;
        }
        $channelBackUrl = $params['channelBackUrl'];
        if($channelBackUrl!=null||$channelBackUrl!=''){
            $url=$url.'&channelBackUrl='.rawurlencode($channelBackUrl);
        }

        LogFunc::_log("debug",'行'.__LINE__.'【go_crh_site】--'.$url.'--','page');

        $checkLoginRet = CommonFunc::checkLoginInfov2('',$account_type,"","");
        if($checkLoginRet['code'] != 0){
            return header('Refresh:0; url='.$url);
        }else{
            $retInfo = $checkLoginRet['result'];
            $durableToken=$retInfo['durableToken'];

            //获取一次性token
            $params = array(
                'commonFunc'=>true,
                'act'=>'instant_tokenApply',
                'durable_token'=>$durableToken
            );
            $res = ApiFunc::api_curl($params, 'marginFundAccountQuery--行：'.__LINE__);
            $res=json_decode($res, true);
            if($res['code'] == 0){
                $instantToken=$res['result']['instantToken'];
                    LogFunc::_log("debug",'行'.__LINE__.'【go_crh_site】--'.rawurlencode($url.'&instant_token='.$instantToken),'page');
                    return header('Refresh:0; url='.$url.'&instant_token='.$instantToken);
            }else{
                return header('Refresh:0; url='.$url);
            }
        }
    }   
    
    public function actionGettoken(){
        $this->startFunc();
        LogFunc::_log("debug",'account_type'.Yii::$app->request->get('account_type'),'page');
        $account_type = (Yii::$app->request->get('account_type')==null) ? SessionFunc::readSession('account_type') : Yii::$app->request->get('account_type');
        $checkLoginRet = CommonFunc::checkLoginInfov2('',$account_type,"","");
        if($checkLoginRet['code'] != 0){
            LogFunc::_log("debug",'获取durableToken失败','page');
            return json_encode(array("ERRORNO"=>0, "GRID0"=> array('code'=>880,'message'=>'获取durableToken失败')));
        }else{
            $retInfo = $checkLoginRet['result'];
            $durableToken=$retInfo['durableToken'];

            //获取一次性token
            $params = array(
                'commonFunc'=>true,
                'act'=>'instant_tokenApply',
                'durable_token'=>$durableToken
            );
            $res = ApiFunc::api_curl($params, 'marginFundAccountQuery--行：'.__LINE__);
            return json_encode(array("ERRORNO"=>0, "GRID0"=> json_decode($res, true)));
        }
    }
    /**
	 * add by yancy
	 * 将web-url处理后的base64格式转换为-base64
	 */
	static public function webToBase64($url){
		$regexp=[['-','+'],['_','/'],['.','=']];
		for($i=0;$i<count($regexp);$i++){
			$url=str_replace($regexp[$i][0],$regexp[$i][1],$url);
		}
		return $url;
	}
     
}
