<?

$apiparams['post_apis'] = array( 'CONTRACT_001','CONTRACT_004','CONTRACT_submit','CONTRACT_SIGN_GROUPNO','CONTRACT_002','QRS','QRMS','LOGGER_COLLECT','QCQPRESAVE','REPWD_006','REPWD_004', 'CWAUB64','ACDS',
	'activity.common.get_all','activity.common.put','QCSQ','QCQP','RESERVE_06','ACT_0001','CWCNO','PACR','PACC','CWSSOS','CWI_01','YAAIDSA','YACTA','YUAAU','CWAUU','YCWIRC_01','YVWC','XYBJSDJG','CWCSRBOS');

	
// 请求以body形式
$apiparams['post_body_apis'] = array('IMAAQ','IMAASCQ', 'IMABQ', 'IMAAS');

//Unloginreq接口允许访问的接口列表
$apiparams['unlogin_apis'] = array('CARA_001','CARA_002','CARA_003','CARA_004','CARA_005','CARA_008','ANNC_1004','ANNC_1001','ANNC_1010','LOGGER_COLLECT','YCWBMQ');

//使用ras加密了请求数据的接口
$apiparams['encrypt_apis'] = array('YCWFPU_01','CWFCPC');

$apiparams['apistring'] = "
;SAS
SAS_002=/yjbapi/sas/session/register|app_id|passport_id|account|account_type|password|expired_time|
SAS_006=/yjbapi/sas/instant_token/apply|durable_token|expored_time|IP|

;PASSPORT
CHANNEL_QUERY=/yjbapi/core/passport/channel/query|channel_id|
UKEY_CREATE=/yjbapi/core/passport/ukey/create|passport_id|channel_id|

;Session
SESSION_001=/yjbapi/session/register|app_id|passport_id|account|account_type|expired_time|IP|
SESSION_002=/yjbapi/session/instant_token/apply|durable_toen|expired_time|IP|

IFS_INVOKE=/yjbapi/front/counter/ifs/invoke|function_no|count|op_station|


;FND系列
FND_0001=/yjbapi/trade/cashmoney/register|fund_account|fund_company_code|fund_code|password|op_station|
FND_0002=/yjbapi/trade/cashmoney/dailyinfo|fund_account|fund_code|trade_date_from|trade_date_to|page_size|page_no|dailyflag|op_station|
FND_0003=/yjbapi/trade/cashmoney/tradeinfo|fund_account|fund_code|trade_date_from|trade_date_to|page_size|page_no|op_station|
FND_0004=/yjbapi/trade/cashmoney/status|fund_account|fund_code|op_station|
FND_0005=/yjbapi/trade/cashmoney/redeem|fund_account|fund_code|redeem_amount|fund_company|password|op_station|
FND_0006=/yjbapi/trade/cashmoney/trigger_amount_update|fund_account|fund_code|auto_apply_threshold|password|op_station|
FND_0008=/yjbapi/trade/cashmoney/fundPerformanceInfo|fund_code|trade_date_from|trade_date_to|page_size|page_no|op_station|
FND_0009=/yjbapi/trade/cashmoney/fundReturnInfo|fund_account|fund_code|trade_date_from|trade_date_to|page_size|page_no|dailyflag|op_station|
FND_0010=/yjbapi/trade/cashmoney/redeem_history|fund_account|page_size|page_no|op_station|
FND_0011=/yjbapi/trade/cashmoney/redeem_undo|withdraw_id|page_size|page_no|fund_account|password|op_station|
FND_0012=/yjbapi/trade/cashmoney/next_tradedate|plus_days|fund_company|op_station|
FND_0013=/yjbapi/trade/cashmoney/first_incomedate|fund_account|fund_code|op_station|
FND_0014=/yjbapi/trade/cashmoney/redeem_amount|fund_account|op_station|
FND_0022=/yjbapi/trade/cashmoney/queryOpenAccountTradeTime|fund_account|fund_code|fund_company|op_station|

;ACT系列
ACT_0001=/yjbapi/account/contract/sign|fund_account|econtract_id|cert_type|cert_sign|cert_plain_text|cert_attachInfo|op_station|attach_info|account_type|business_type|
ACT_0003=/yjbapi/account/contract/info|econtract_id|returnContent|include_content|op_station|
ACT_0005=/yjbapi/user/account/assetSummaryInfo|fund_account|password|money_type|op_station|
ACT_0009=/yjbapi/user/account/userInfoQuery|fund_account|op_station|

ACT_0011=/yjbapi/user/subscribe/userMessageSubscribeQuery|fund_account|subscribe_source|op_station|
ACT_0012=/yjbapi/user/subscribe/userMessageSubscribeUpdate|fund_account|message_no|subscribe_source|subscribe_channel|op_type|op_station|
ACT_0013=/yjbapi/account/contract/text|econtract_id|op_station|
ACT_0014=/yjbapi/core/bank/bankAccountInfo|fund_account|password|op_station|


;信用存管银行列表查询
BNK_0004=/yjbapi/core/bank/creditClearingBankList
;BNK系列
BNK_0001=/yjbapi/core/bank/depositoryBankList|bank_no|terminal_type|business_type|return_actcnt|op_station|

;BANK_TRADE系列
BANK_TRADE_0001=/yjbapi/core/bank/bankAccountInfo|fund_account|password|op_station|
BANK_TRADE_0002=/yjbapi/trade/bank/balanceQuery|branch_no|fund_account|password|bank_password|money_type|op_station|
BANK_TRADE_0003=/yjbapi/trade/bank/transferMoney|branch_no|fund_account|password|bank_password|money_type|occur_balance|direction|fund_password|op_station|
BANK_TRADE_0004=/yjbapi/trade/bank/queryBalanceFromFundLog|serialNO|op_station|
BANK_TRADE_0005=/yjbapi/trade/bank/history|fund_account|page_size|page_no|op_station|
BANK_TRADE_0011=/yjbapi/trade/bank/TimeInfo|serialNO|op_station|
TRADE_BANK_0006=/yjbapi/trade/bank/allHistory|fund_account|page_size|page_no|op_station|

;PAST系列
PASPT_0011=/yjbapi/core/passport/authenticate|accountType|accountId|password|op_station|

;时间服务接口
TS_002=/yjbapi/core/timeservice/calcTradeDay|exchange_type|date|time|ifSkip|nextN|op_station|
TS_001=/yjbapi/core/timeservice/checkTransactionTime|type|exchange_type|time_kind|date|time|op_station|
TS_11=/yjbapi/trade/cashmoney/queryAutoApplyTradeTime|date|time|op_station|
TS_12=/yjbapi/trade/cashmoney/queryWithDrawalsTradeTime|date|op_station|
TS_004=/yjbapi/core/timeservice/queryTransactionTime|type|exchange_type|time_kind|op_station|

;同一验证服务
UAS_001=/yjbapi/core/uas/sms_push|mobile|template_no|expire_minutes|op_station|
UAS_002=/yjbapi/core/uas/validate|captcha_id|captcha_code|expire_now|op_station|

;帐户视图
UACT_006=/yjbapi/user/account/queryUserAccountsInfo|client_id|op_station|

;信用额度
TRADE_MARGIN_0003=/yjbapi/trade/margin/getCreditLines|client_id|fund_account|op_station|
;融资融券
MARGIN_0003=/yjbapi/account/margin/open_template|fund_account|
MARGIN_0002=/yjbapi/account/margin/credit/score
MARGIN_0008=/yjbapi/account/margin/clientCreditInfoUpdate|client_id|restrict_secu_flag|restrict_secu_info|secu_code|secu_name|secu_shares|restrict_endDate|senior_person_flag|senior_person_info|company_code|company_name|position|position_endDate|shareholder_flag|shareholder_info|secu_code|holder_account|secu_shares|relatedholder_flag|relatedholder_info|holder_name|holder_idno|holder_relation|holder_account|credit_account_flag|business_type|
;个性化设置视图
SUS_1=/yjbapi/user/profile/setUserSetting|passport_id|terminal_type|setting_id|setting_value|op_station|
SUS_4=/yjbapi/user/profile/queryUserSetting|passport_id|setting_id|terminal_type|op_station|

;问卷
QQN_1=/yjbapi/account/questionnaire/saveUserQuestionnaireResult|questionnaire_no|fund_account|preengage_id|client_id|credit_fund_account|user_answers|op_station|
;判断是否为佣金宝客户
UACT_007=/yjbapi/user/account/isyjbclient|account|account_type|op_station|

;UACT_008  现金理财概况信息
UACT_008=/yjbmobile/api/cashmoney/profile|fund_account|fund_code|op_station|
;UACT_009  现金理财取款相关查询
UACT_009=/yjbmobile/api/cashmoney/withdraw|fund_account|fund_code|password|fund_company|plus_days|op_station|
;UACT_010  转账相关
UACT_010=/yjbmobile/api/trade/transfer|fund_account|money_type|password|op_station|
;本地账户信息查询（依赖UFX）
UACT_012=/yjbapi/user/stockaccount/holderinfo/query|client_id|
;补开证券（普通&信用）
UACT_01=/yjbapi/account/reopenstock/stock/reopen|client_id|fund_account|en_exchange_type|en_holder_kind|stock_account_str|
;补开户在途工单查询
UACT_02=/yjbapi/account/reopenstock/businprocess/query|client_id|

;系统清算时间
SYS_001=/yjbapi/syscommon/systatus/getSystemStatus|systemType|op_station|
;清密-预设密码
ACRA_02=/yjbapi/account/cara/reset/pwd/pre|mobile_no|fund_account|trade_pwd|fund_pwd|same_flag|op_station|
;清密-获取银证转账通知
ACRA_03=/yjbapi/account/cara/reset/pwd/bank/transfer/request|fund_account|op_station|

;资金流水查询
STK_007=/yjbapi/trade/stock/getFundHistory|fund_account|page_size|page_no|fund_date_from|fund_date_to|data_source|op_station|



;系统时间查询
UACT_011=/yjbmobile/api/core/servertime|op_station|
;我的护照-首页视图
UACT_015=/yjbmobile/api/user/passport|client_id|
;订阅提示查询
SUB_004=/yjbapi/user/subscribe/messageSubscribeTxtQuery|subscribe_source|message_no|op_station|
;新服务订阅状态更新
SUB_002=/yjbapi/user/subscribe/update|fund_account|channel_id|service_id|status|op_station|
;订阅提示查询
SUB_005=/yjbapi/user/subscribe/userService|fund_account|channel_id|op_station|

;dog监控专用功能号
PASPT_0013=/yjbapi/core/passport/getPassport|passportId|accountType|accountId|op_station|




;--------------------线上柜台--------------
UACT_003=/yjbapi/user/account/userInfoQuery|fund_account|op_station|client_id|
UACT_005=/yjbapi/front/counter/account/passport|client_id|op_station|
UACT_101=/yjbapi/account/econtract/list|op_station|
UACT_102=/yjbapi/account/contract/info|econtract_id|include_content|op_station|
UACT_103=/yjbapi/account/contract/sign_status|fund_account|econtract_id|
UACT_013=/yjbapi/user/csdcaccount/request/send|op_station|client_name|id_kind|id_no|client_id|
UACT_014=/yjbapi/user/csdcaccount/response/get|op_station|request_no|
UACT_016=/yjbapi/user/account/mobileTelAmount|mobile_tel|op_station|

;身份证更新信息查询
UACT_IDUPDATE_CHECKANDQUERY=/yjbapi/account/archive/idupdate/checkandquery|client_id|op_station|
UACT_IDUPDATE_QUERY=/yjbapi/account/archive/idupdate/query|client_id|op_station|
;适当性和准入条件检测
UACT_SUITABLY_CHECKCONDITION=/yjbapi/counter/web/proper/check|client_id|right|password|check_type|op_station|
;查询用户的风险等级(有最低风险级别)
QUERY_RISK_LEVEL=/yjbapi/front/counter/proper/queryRiskLevel|client_id|op_station|
		
MARGIN_0004=/yjbapi/account/overQuotaApply|preengage_id|client_id|brance_no|credit_type|credit_coefficient|credit_quota|app_status|op_station|

;额度调整模块
;信用账户调额前的检查
CAPI_001=/yjbapi/front/counter/credit/prepCreditLineCalculation|client_id|fund_account|credit_fund_account|password|op_station|
;额度调整
CAPI_002=/yjbapi/front/counter/credit/reCreditRating|client_id|fund_account|credit_fund_account|password|op_station|
;深度征信
CAPI_003=/yjbapi/front/counter/credit/overQuotaApply|client_id|fund_account|credit_fund_account|password|branch_no|op_station|
;补开户（普通股东户&信用股东户）
CAPI_004=/yjbapi/front/counter/account/reopenStockHolderAccount|client_id|fund_account|exchange_kind|stock_account|en_exchange_kind|stock_account_str|distrib_stkacct_str|csdc_distrib_stock_acct|sc_open_flag|password|fund_account_type|branch_no|op_station|
;获取系统参数
SYSCOMMON=/yjbapi/syscommon/profile/parameter|param_key|business_type|op_station|

;信用调额模块--newnew
;查询当前额度
CREDIT_CURRENT_QUOTA=/yjbapi/account/margin/recredit/currentQuota|client_id|fund_account|credit_fund_account|op_station|password|
;查询当前状态		
CREDIT_QUERY_STATUS=/yjbapi/account/margin/recredit/queryStatus|client_id|fund_account|credit_fund_account|op_station|password|
;调整额度
CREDIT_RECREDIT_RATING=/yjbapi/account/margin/recredit/reCreditRating|client_id|fund_account|credit_fund_account|op_station|password|		
;深度征信
CREDIT_OVERQUOTA_APPLY=/yjbapi/account/margin/recredit/overQuotaApply|branch_no|client_id|fund_account|credit_fund_account|op_station|password|

;A股补开查询--newnew
STOCKHOLDER_QUERY=/yjbapi/account/csdcweb/stockholder/normal/status/info|client_id|fund_account|password|op_station|op_entrust_way|		
STOCKHOLDER_LISTINFO=/yjbapi/account/csdcweb/stockholder/normal/list/info|client_id|fund_account|op_station|op_entrust_way|password|
STOCKHOLDER_OPEN=/yjbapi/account/csdcweb/stockholder/normal/open|client_id|fund_account|op_station|op_entrust_way|password|sh_open_type|sh_stock_account|sz_open_type|sz_stock_account|
STOCKHOLDER_ASYNC_OPEN=/yjbapi/account/csdcweb/stockholder/normal/async/open|client_id|fund_account|op_station|op_entrust_way|password|sh_open_type|sh_stock_account|sz_open_type|sz_stock_account|
STOCKHOLDER_ASYNC_QUERY=/yjbapi/account/csdcweb/stockholder/normal/async/result/query|op_station|request_no|

;A股补开视频资料
CWVAIR=/yjbapi/counter/web/video/archive/integrities/result|client_id|fund_account|op_station|
STOCKHOLDER_AUDIT_QUERY=/yjbapi/counter/web/stockholder/normal/audit/query|client_id|fund_account|market|op_station|

;yjbAPI 异步请求IFS接口
CASYNC_001=/yjbapi/front/counter/asynchronous/invoke|function_no|client_id|password|fund_account|exchange_kind|en_exchange_kind|fund_account_type|branch_no|stock_account|gem_train_flag|sub_risk_date|op_station|COOKIE_CT|COOKIE_JD|model_no|


;CASYNC_002 获取IFS异步调用的结果
CASYNC_002=/yjbapi/front/counter/asynchronous/queryresult|request_no|op_station|

;------------------------------------------  新股申购------------------------------------------


;查询可用资金
TRADE_IPO_AVAILABLE_CAPITAL=/yjbapi/trade/ipo/available_capital|branch_no|client_id|fund_account|password|op_station|
;待摇号
TRADE_IPO_RANOMSTOCK=/yjbapi/trade/ipo/match_info|branch_no|client_id|fund_account|password|op_station|
;待缴款
TRADE_IPO_LUCKY_INFO=/yjbapi/trade/ipo/lucky_info|branch_no|fund_account|password|client_id|op_station|
;待上市 - 暂时不需要
;TRADE_IPO_LIST_INFO=/fs/03be60adc716431b8f9b5e70e2fa63db|branch_no|client_id|fund_account|password|op_station|
;已申购页面tab信息   待摇号 待上市 待缴款全部tab页
TRADE_IPO_PURCHASED_TAB=/yjbapi/front/trade/ipo/purchased_tab|branch_no|client_id|fund_account|password|tab_type|op_station|



;主页信息
FRONT_TRADE_IPO_INDEX=/yjbapi/front/trade/ipo/index|fund_account|password|branch_no|client_id|op_station|
;今日可用申购
TRADE_IPO_TODAY_IPO=/yjbapi/trade/ipo/today_ipo|fund_account|password|client_id|op_station|
;T+N日可用申购
TRADE_IPO_FUTURE_IPO=/yjbapi/trade/ipo/future_ipo|current_date|op_station|
;新股详情
TRADE_IPO_IPO_DETAIL=/yjbapi/trade/ipo/ipo_detail|stock_code|op_station|
;一键申购
TRADE_IPO_ENTRUST=/yjbapi/trade/ipo/entrust|fund_account|password|branch_no|client_id|exchange_type_str|stock_code_str|entrust_amount_str|entrust_price_str|op_entrust_way|op_station|


;SSO 年终账单部分
SSO_001=/yjbapi/core/sso/registerOnlineUsersSession|app_id|passport_id|account|cipher_content|cipher_type|auth_type|op_station|
SSO_002=/yjbapi/core/sso/applyInstantToken|durable_token|key_info|op_station|

;创业板登记检查 请求发送
GEMCHECK_REQUEST_SEND=/yjbapi/user/gemcheck/request/send|client_id|stock_account|op_station|
;创业板登记检查 结果获取
GEMCHECK_RESPONSE_GET=/yjbapi/user/gemcheck/response/get|request_no|op_station|


;通过日期查询ipo详情(DM)
QUERY_IPO_INFO_BY_DAY=/yjbapi/trade/ipo/query_ipo_info_by_date|start_date|op_station|



;获取普通开户费率模板
CPY_007=/yjbapi/core/company/stock/open_template|product_id|branch_no|op_station|
;查询港股通开户的步骤
UACT_019=/yjbapi/user/account/qryBusinProcessStatus|client_id|busin_op_type|fund_account|eligPaperStr|econIds|

;历史中签信息查询
HISTORY_LIST_INFO=/yjbapi/trade/ipo/history_list_info|fund_account|start_date|end_date|op_station|


;---------------------------------------------个人信息-------------------------------------------
MARGIN_0001=/yjbapi/account/margin/isacceptmargin|op_station|

;新股申购增夜市委托判断
IPO_SYSTEM_STATUS=/yjbapi/trade/ipo/system_status|op_station|


;----风险警示权限新增
;风险警示权限开通
RISKMODULE_01=/yjbapi/front/counter/riskwarn/openright|client_id|fund_account|exchange_type|stock_account|econtract_id|password|COOKIE_CT|COOKIE_JD|op_station|
;风险警示权限注销
RISKMODULE_02=/yjbapi/front/counter/riskwarn/closeright|client_id|fund_account|exchange_type|stock_account|password|COOKIE_CT|COOKIE_JD|op_station|
;公告查询
ANNC_1004=/yjbapi/core/announcement/query|id|type|subtype|op_station|
ANNC_1001=/yjbapi/core/announcement/create|id|terminal_type|type|title|publisher|status|validfrom|validto|remark|content_type|content|subtype|optype|operator|op_station|
ANNC_1010=/yjbapi/core/announcement/group/query/type|id|type|sub_type|op_station|

;----清密新增
;获取验证码
;CARA_001=/yjbapi/front/counter/cara/captcha/img|msg_height|msg_width|font_size|op_station|
;获取验证码（找回账户_验证客户信息）
;CARA_002=/yjbapi/front/counter/cara/client/info/check|captcha_id|client_name_input|identity_card_input|mobile_no_input|captcha_code_input|op_station|
;短信验证码
;CARA_003=/yjbapi/front/counter/cara/captcha/msg|mobile_no|op_station|
;图片验证码校验
CARA_008=/yjbapi/counter/web/cara/captcha/img/chk|captcha_id|captcha_code_input|


;验证短信验证码并返回客户信息
;CARA_004=/yjbapi/front/counter/cara/fundaccount/get|mobile_no|captcha_code_input|msg_captcha_id|op_station|
;发送短信通知
;CARA_005=/yjbapi/front/counter/cara/fundaccount/msg/send|mobile_no|op_station|
;重置密码_验证客户信息
CARA_006=/yjbapi/front/counter/cara/client/info/check/reset|captcha_id|fund_account_input|client_name_input|mobile_no_input|captcha_code_input|op_station|
;系统状态查询
;SYS_001=/yjbapi/syscommon/systatus/getSystemStatus|systemType|op_station|
		

;获取验证码--newnew
CARA_001=/yjbapi/counter/web/cara/captcha/img|msg_height|msg_width|font_size|op_station|nocheckLogin|
;获取验证码（找回账户_验证客户信息）--newnew
CARA_002=/yjbapi/counter/web/cara/client/info/check|captcha_id|client_name_input|identity_card_input|mobile_no_input|captcha_code_input|op_station|nocheckLogin|mobile_no_input_valid|
;短信验证码 --newnew
CARA_003=/yjbapi/counter/web/cara/captcha/msg|mobile_no|op_station|nocheckLogin|validate_id|
;验证短信验证码并返回客户信息 --newnew
CARA_004=/yjbapi/counter/web/fundaccount/get|mobile_no|captcha_code_input|msg_captcha_id|op_station|nocheckLogin|validate_id|
;发送短信通知  --newnew
CARA_005=/yjbapi/counter/web/cara/fundaccount/msg/send|mobile_no|op_station|nocheckLogin|validate_id|
		
		
;身份拦截
CHECKCOMPLIANCE=/yjbapi/user/account/checkCompliance|fund_account|client_id|op_station|

;沪A指定交易申报单查询
CAFA_003=/yjbapi/front/counter/account/designating|client_id|fund_account|stock_account|password|op_station|

;对账单
;BILL_01=/yjbapi/bill/web/entrance|fund_account|op_station|

;webapp-sso 桥接模块
SSO_005=/yjbapi/core/sso/getCipherTokenByDurableToken|durable_token|op_station|
UPS_0002=/yjbapi/upsservice|actionId|params|op_station|
PAYLOAD=/yjbmobile/api/payload|IP|

;查询客户号:
ACCOUNT_USERINFOQUERY=/yjbapi/user/account/userInfoQuery|fund_account|op_station|
;交易日时间查询
FRONT_TRADE_DAY=/yjbapi/front/core/timeservice/calcTradeDays|nextN|time|date|exchange_type|ifSkip|op_station|

;佣金宝2.0 开户新配置 (注意: 功能号必须用大写,小写会找不到对应的unique_key)
;added by jin
;发送手机验证码
UAS_SMS_PUSH=/yjbapi/core/uas/sms_push|mobile|template_no|expire_minutes|
;创建预约号
PREAPPOINT_CREATE=/yjbapi/account/openstock/preappoint/create|mobile_no|captcha_code|captcha_id|business_type|terminal_type|product_id|
;查询预约状态
PREAPPOINT_STATUS_QUERY=/yjbapi/account/openstock/preappoint/status/query|preengage_id|
;查询营业部列表
BRANCH_LIST=/yjbapi/core/company/getBranchList|open_account_type|
;存管银行列表查询
DEPOSITORY_BANK_LIST=/yjbapi/core/bank/depositoryBankList|bank_no|terminal_flag|terminal_type|business_type|return_actcnt|
;选择存管银行
OPENSTOCK_BANK_BIND=/yjbapi/account/openstock/bank/bind|preengage_id|bank_account|bank_no|bank_password|terminal_type|
;获取选择的存管银行
QUERY_DEPOSITORY=/yjbapi/account/openstock/bank/get|preengageId|terminal_type|
;银行卡BIN信息查询
BANK_AUTH_001=/yjbapi/core/chinapay/bin_query|card_no|
;电子协议基本信息查询
CONTRACT_INFO=/yjbapi/account/contract/info|econtract_id|include_content|
;电子协议签署
;CONTRACT_SIGN=/yjbapi/account/openstock/contract/sign|preengage_id|econtract_no|cert_type|cert_sign|cert_plain_text|cert_attach_info|
;选择开户类型
OPENSTOCK_TYPE_CHOOSE=/yjbapi/account/openstock/type/choose|preengage_id|exchange_type|terminal_type|
;设置资金密码
OPENSTOCK_PASSWD_SET=/yjbapi/account/openstock/passwd/set|preengage_id|trade_passwd|fund_passwd|terminal_type|
;银证鉴权
OPENSTOCK_BANK_AUTH=/yjbapi/account/openstock/bank/auth|preengage_id|mobile|
;完成银证鉴权步骤
OPST_019=/yjbapi/account/openstock/bank/finish_auth|preengage_id|terminal_type|
;提交视频见证结果
SUBMIT_VIDEO_RESULT=/yjbapi/core/operator/video/result/submit|preengage_id|status|operator_id|
;发起图片人工审核
START_IMG_CHECK=/yjbapi/account/openstock/archive/image/audit/init|preengage_id|terminal_type|
;根据产品获取营业部息查询
UACT_0011=/yjbapi/user/account/openClientInfoQuery|mobile|
;获取服务器时间
YJBMOBILE_SERVERTIME=/yjbmobile/api/core/servertime
;批量获取字典分类子项
QUERY_CATEGORY_ITEMS_QUERY_LIST=/yjbapi/syscommon/profile/category/items/queryList|category_code|
;查询用户信息身份证信息
QUERY_PERSONAL_INFO=/yjbapi/account/openstock/preclientinfo/idcard|preengage_id|
;获取字典分类子项
QUERY_CATEGORY_ITEMS=/yjbapi/syscommon/profile/category/items|category_code|

;开户2.0部分


;时间服务接口
CALC_TRADE_DAY=/yjbapi/timeservice/calcTradeDay|exchange_type|date|time|ifSkip|nextN|
CHECK_TRANSACTION_TIME=/yjbapi/timeservice/checkTransactionTime|type|exchange_type|time_kind|date|time|

;取营业部列表
;http://**************:8080/yjbapi/company/company/getBranchList?open_account_type=1
COMPANY_0001=/yjbapi/company/company/getBranchList|open_account_type|

;-----------------------------by 徐伟斌 ********--------------------------
;视频开户
;提交视频见证申请
OPST_010=/yjbapi/account/openstock/match/apply|preengage_id|ip_address|terminal_type|
;查询视频见证撮合结果
OPST_011=/yjbapi/account/openstock/match/result|request_id|preengage_id|

;added by wangshiwang
;问卷服务->问卷查询
QUERYQUESTIONNAIRE=/yjbapi/account/questionnaire/queryQuestionnaire|questionnaire_no|
;上传档案信息
UPLOAD=/yjbapi/account/openstock/archive/image/uploadbase64|preengage_id|image_file|image_type|islastone|terminal_type|image_flag|image_file_base64|
;适当性评估
ESTIMATE=/yjbapi/account/openstock/questionnaire/estimate|questionnaire_no|user_answers|preengage_id|terminal_type|business_type|
;适当性评估确认
ESTIMATE_CONFIRM=/yjbapi/account/openstock/questionnaire/estimate/confirm|questionnaire_no|status|preengage_id|terminal_type|
;提交客户回访问卷
QUESTIONAIRE_VISIT=/yjbapi/account/openstock/questionnaire/visit|questionnaire_no|user_answers|preengage_id|terminal_type|
;查询开户结果
;查询视频见证驳回建议操作
VIDEO_REJECT_RESULT=/yjbapi/core/operator/video/reject/query|preengage_id|

;获取系统字典分类详细
PROFILE_CATEGORY_ITEMS=/yjbapi/syscommon/profile/category/items|category_code|

;查询视频见证状态
OPST_029=/yjbapi/account/openstock/video/result|preengage_id|
;查询开户结果
OPST_025=/yjbapi/account/openstock/result|preengage_id|

;获取指定题号答案
OPST_030=/yjbapi/account/openstock/questionnaire/queryConfirmResult|preengage_id|questionnaire_items_no|
;获取系统参数列表
PARAMETER_LIST=/yjbapi/syscommon/profile/parameterList|param_key|business_type|
;查询审核结果列表
CUSTOMERAPPLY_QUERY_RESULT=/yjbapi/core/operator/customerapply/query/result|preengage_id|business_type|
;批量获取字典分类子项
QUERY_CATEGORY_ITEMS_QUERY_LIST=/yjbapi/syscommon/profile/category/items/queryList|category_code|
;查询银行开户结果
OPST_031=/yjbapi/account/openstock/bank/result|preengage_id|


;现金产品表现数据查询
CASHMONEY_FUNDPERFORMANCEINFO=/yjbapi/trade/cashmoney/fundPerformanceInfo|fund_code|trade_date_from|trade_date_to|page_size|page_no|
;基金现金理财产品购买
CASHMONEY_SUBSCRIBE=/yjbapi/account/openstock/cashmoney/subscribe|preengage_id|fund_code|terminal_type|


;----统一验证服务
;申请验证码ID
UAS_003=/yjbapi/core/uas/id/new|len|complex_random|char_len|int_len|op_station|
;获取验证码内容
UAS_004=/yjbapi/core/uas/get_value|captcha_id|op_station|
;获取验证码图片
UAS_005=/yjbapi/core/uas/img|captcha_id|width|height|font_size|font_color|bg_color|noise_color|op_station|
;获取验证码图片base64值
UAS_006=/yjbapi/core/uas/img|captcha_id|width|height|font_size|font_color|bg_color|noise_color|op_station|
;获取手机号的验证码（返回该手机号产生的最近一个未失效的验证码）
UAS_007=/yjbapi/core/uas/get_value_by_mobile|mobile_no|op_station|
;发送通知短信
UAS_008=/yjbapi/core/uas/sms_inform_send|mobile_no|template_no|template_params|op_station|


;-------------------------沪深版港股通权限开通---------------
;港股通状态查询
OPENGGT_001=/yjbapi/front/counter/hkstock/queryStatus|client_id|fund_account|exchange_type|password|op_station|
;条件检查接口
OPENGGT_002=/yjbapi/front/counter/hkstock/conditionCheck|client_id|fund_account|exchange_type|password|op_station|
;获取知识测评状态
OPENGGT_003=/yjbapi/front/counter/hkstock/queryEligibleStatus|client_id|exchange_type|password|op_station|
;获取港股通问卷
OPENGGT_004=/yjbapi/front/counter/hkstock/queryEligibleQuestion|paper_type|organ_flag|op_station|
;提交港股通问卷
OPENGGT_005=/yjbapi/front/counter/hkstock/submitEligibleAnswer|client_id|paper_type|answers|organ_flag|password|op_station|
;获取协议
OPENGGT_006=/yjbapi/front/counter/hkstock/queryProtocol|econtract_id|op_station|
;签署协议并开通港股通
OPENGGT_007=/yjbapi/front/counter/hkstock/signProtocolAndOpen|client_id|fund_account|exchange_type|stock_account|econtract_id|password|op_station|
;签署协议
OPENGGT_008=/yjbapi/front/counter/hkstock/signProtocol|client_id|fund_account|econtract_id|password|op_station|
;开通港股通
OPENGGT_009=/yjbapi/front/counter/hkstock/openRight|client_id|fund_account|exchange_type|stock_account|password|op_station|

;yjbAPI同步调用IFS通用接口
ASYNCIFS=/yjbapi/front/counter/ifs/invoke|function_no|client_id|password|fund_account|op_station|COOKIE_CT|COOKIE_JD|model_no|sec_relation_name|sec_relation_phone|socialral_type|

;电子协议签署（入库MySQL+柜台）
CAFA_004=/yjbapi/front/counter/account/contract/sign|client_id|econtract_id|fund_account|op_station|summary|business_type|password|

;交易日时间查询
FRONT_TRADE_DAY=/yjbapi/front/core/timeservice/calcTradeDays|nextN|time|date|exchange_type|ifSkip|op_station|

;基金警示信息查询、留痕接口配置password入参
STK_011=/yjbapi/trade/stock/operate/do_trace|IP|branch_no|client_id|fund_account|password|exchange_type|stock_code|operate_type|op_entrust_way|trade_type|op_station|
STK_012=/yjbapi/trade/stock/notice_info_query|IP|branch_no|client_id|fund_account|password|exchange_type|stock_code|op_entrust_way|trade_type|op_station|

;资金占用天数查询
REVERSE_REPOS_CONFIRM_QUERY=/yjbapi/reverse/repos/stock_code_confirm/query|client_id|fund_account|password|exchange_type|branch_no|stock_codes|op_station|

;IFS登录接口
IFS_LOGIN=/yjbapi/ifs/proxy/clientlogin|account_content|password|op_station|input_content|op_entrust_way|password_type|

;CASYNC_003 获取信用系统交易时间
CASYNC_003=/yjbapi/front/counter/businTime|busin_type|op_station|

;退市整理条件检查
DELIST_001=/yjbapi/front/counter/delist/conditionCheck|client_id|fund_account|exchange_types|password|op_station|
;退市整理权限开通
DELIST_002=/yjbapi/front/counter/delist/openright|client_id|fund_account|exchange_type|stock_account|econtract_id|branch_no|password|op_station|
;退市整理权限注销
DELIST_003=/yjbapi/front/counter/delist/closeright|client_id|fund_account|exchange_type|stock_account|op_station|
;开通条件检查(废弃)
DELIST_004=/yjbapi/front/counter/delist/checkopencondition|client_id|fund_account|exchange_type|op_station|
:退市整理状态查询
DELIST_005=/yjbapi/front/counter/delist/queryStatus|client_id|fund_account|op_station|

;协议签署
CONTRACT_001=/yjbapi/account/contract/group_list|branch_no|client_id|fund_account|op_entrust_way|op_station|password|password_type|group_nos|include_content|
CONTRACT_002=/yjbapi/account/contract/sign_by_groupno|branch_no|client_id|fund_account|op_entrust_way|op_station|password|password_type|group_nos|sub_info|business_flag|account_type|
CONTRACT_003=/yjbapi/account/contract/text|econtract_id|op_station|
CONTRACT_004=/yjbapi/account/contract/dontsignufdb/query_by_groupno|branch_no|client_id|fund_account|op_entrust_way|op_station|password|password_type|group_nos|include_content|
CONTRACT_submit=/yjbapi/account/contract/sign_by_groupno|branch_no|client_id|fund_account|op_entrust_way|op_station|password|password_type|group_nos|sub_info|business_flag|account_type|
CONTRACT_SIGN_GROUPNO=/yjbapi/account/contract/sign_by_groupno_without_e|branch_no|client_id|fund_account|op_entrust_way|op_station|password|password_type|group_nos|sub_info|business_flag|account_type|
;适当性风险等级自动测评结果确认
APPROPRIATE_SURE=/yjbapi/inverstment/adviser/appropriate/auto_check/sure|branch_no|op_entrust_way|op_station|client_id|fund_account|password|password_type|paper_type|autoeligtest_status|prodta_no|

;合规留痕
YACTA=/yjbapi/account/contract/trace/apply|account_id|account_type|business_type|op_id|addition|op_info|op_station|remark|

;获取验证码
HS830005=op_station|mobile_tel|op_station|
;验证码验证
HS831002=client_id|client_name|id_kind|id_no|csdc_open_flag|op_station|
;获取图片验证码
HS831324=count|char_config|imagecode_type|width|height|font_color|font_color_str|background_color|op_station|
HS831325=count|char_config|imagecode_type|op_station|
;客户登陆
HS831000=op_entrust_way|op_station|input_content|account_content|content_type|password|password_type|image_code|COOKIE_CT|COOKIE_JD|op_station|
;股东账号查询
HS831314=client_id|op_station|
;系统工作时间检测
HS831321=client_id|op_station|
;资产账户查询
HS831327=client_id|fund_account|op_station|
;中登股东账户信息应答获取
HS831319=client_id|exchange_kind|en_exchange_kind|op_station|
;补开证券账户
HS831320=client_id|fund_account|exchange_kind|stock_account|en_exchange_kind|stock_account_str|csdc_distrib_stock_acct|op_station|
;创业板登记查询
HS831316=client_id|stock_account|gem_train_flag|sub_risk_date|op_station|
;通用业务办理条件检查
HS831016=client_id|busin_op_type|op_station|
;中登股东账户信息查询
HS831318=client_id|exchange_kind|en_exchange_kind|op_station|
;创业板等级检查
HS831326=client_id|stock_account|op_station|
;融资融券合同查询
HS835072=client_id|fund_account|en_contract_status|request_num|position_str|op_station|
;客户资金精确查询
HS832001=op_entrust_way|client_id|client_id|paper_type|money_type|fund_account|op_station|
;中登股东账户信息查询请求
HS831034=op_station|client_name|id_kind|id_no|op_station|
;中登股东账户信息应答获取
HS831035=op_station|request_no|op_station|
;征信指标获取
HS831214=client_id|paper_type|op_station|
;征信问卷提交
HS831213=client_id|paper_type|paper_answer|op_station|
;客户征信结果查询
HS831216=client_id|paper_type|op_station|
;开户结果查询
HS831008=op_station|client_id|op_station|
;电子协议补充签署
HS831323=client_id|busin_op_type|econtract_id|econtract_name|econtract_md5|cert_type|cert_sign|plain_text|summary|econtract_data|op_station|
;柜台客户网上注册查询
HS831015=client_id|mobile_tel|op_station|
;证券账户开户申请
HS831012=client_id|fund_account|exchange_kind|en_exchange_kind|stock_account_str|distrib_stkacct_str|csdc_distrib_stock_acct|busin_op_type|op_station|
;客户风险测评试卷查询
HS831204=paper_type|organ_flag|prodta_no|op_station|
;客户风险答卷提交
HS831205=client_id|paper_answer|paper_type|prodta_no|op_station|
;客户风险答卷查询
HS831206=client_id|paper_type|prodta_no|op_station|
;信用资产账户开户/同步密码
HS831032=client_id|model_no|password|fund_password|mobile_tel|e_mail|op_station|
;见证结果查询
HS831317=op_station|client_id|op_station|
;离线视频见证预约
HS831005=client_id|qq|deal_time_str|op_station|
;离线视频见证预约
HS831322=client_id|witness_type|busin_op_type|qq|deal_time_str|op_station|
;信用账户开户状态查询
HS831019=client_id|op_station|
;客户模板查询
HS831031=branch_no|model_no|asset_prop|organ_flag|client_id|op_station|
;获取问卷回访
HS831207=client_id|op_station|
;风险测评
HS831204=paper_type|organ_flag|prodta_no|op_station|
;风险测评提交
HS831205=client_id|paper_answer|paper_type|prodta_no|op_station|
;电子协议签署
HS831203=client_id|econtract_id|econtract_name|econtract_md5|cert_type|cert_sign|plain_text|summary|econtract_data|op_station|
;存管银行绑定
HS831009=client_id|fund_account|money_type|bank_no|bank_account|bk_password|busin_op_type|op_station|
;融资融券合同合约录入
HS835071=client_id|fund_account|settpay_mode|begin_date|end_date|crdt_ratio_stg|render_stop_flag|contract_rights|product_id|fin_rate|slo_rate|prefer_type|pc_begin_date|pc_end_date|pc_term|comb_no|comb_status|vast_flag|remark|op_station|
;融资融券合约查询
HS835072=client_id|fund_account|en_contract_status|request_num|position_str|op_station|
HS835073=client_id|fund_account|en_contract_status|request_num|position_str|op_station|
;客户邮寄对账信息获取
HS831147=client_id|fund_account|op_station|
;客户信用资产信息获取
HS835013=op_entrust_way|client_id|fund_account|money_type|op_station|
;预征信额度计算
HS831218=client_id|paper_type|paper_answer|op_station|
;客户信用评级
HS831215=client_id|paper_type|op_station|
;补开基金账户
HS831028=client_id|fund_account|en_fund_company|op_station|
;修改密码
HS831148=client_id|fund_account|password_type|password|password_old|op_station|


;jinyuanche
;证券委托查询
HS833004=op_entrust_way|client_id|fund_account|exchange_type|stock_account|stock_code|query_direction|sort_direction|report_no|action_in|locate_entrust_no|query_type|query_mode|position_str|request_num|op_station|
;上海指定交易
HS833071=client_id|fund_account|exchange_type|stock_account|seat_no|stock_code|entrust_amount|entrust_price|entrust_bs|entrust_prop|batch_no|op_station|


;业务时间判断
HS831321=client_id|op_station|
;股东账号查询
HS831314=|client_id|op_station|

;创业板登记检查
HS831326=client_id|stock_account|op_station|
;填写第二联系人
HS831315=client_id|sec_relation_name|sec_relation_phone|socialral_type|op_station|
;创业板登记
HS831316=client_id|stock_account|gem_train_flag|sub_risk_date|op_station|
;获取图片验证码
HS831324=count|char_config|imagecode_type|width|height|font_color|font_color_str|background_color|op_station|
HS831325=count|char_config|imagecode_type|op_station|


;------------------------------------------  港股通线上柜台迭代2配置-----------------------------

;IFS数据字典查询
HS830002=branch_no|dict_entry|subentry|dict_prompt|op_station|
;港股通权限开通
HS833208=client_id|fund_account|stock_account|fare_kind_str|op_station|


;-------港股通权限开通新增
;IFS数据字典查询
HS830002=branch_no|dict_entry|subentry|dict_prompt|op_station|
;港股通权限开通
HS833208=client_id|fund_account|stock_account|fare_kind_str|model_no|op_station|
;获取下一个交易日
HS830015=op_entrust_way|finance_type|exchange_type|stage_num|init_date|op_station|
;通用业务办理进度查询
HS831042=client_id|busin_op_type|acpt_id|op_station|

;--------风险警示新增配置
;风险警示协议签署
HS831047=client_id|fund_account|exchange_type|stock_account|op_station|
;风险警示协议注销
HS831048=client_id|fund_account|exchange_type|stock_account|op_station|


;身份图像识别
HS831301=regcust_id|mobile_tel|qq|image_no|image_data|op_station|
;IFS上传档案信息
HS831304=regcust_id|client_id|id_no|client_name|mobile_tel|qq|image_no|image_data|op_station|

;证券委托查询
HS833004=op_entrust_way|client_id|fund_account|exchange_type|stock_account|stock_code|query_direction|sort_direction|report_no|action_in|locate_entrust_no|query_type|query_mode|position_str|request_num|op_station|
;上海指定交易
HS833071=client_id|fund_account|exchange_type|stock_account|seat_no|stock_code|entrust_amount|entrust_price|entrust_bs|entrust_prop|batch_no|op_station|

;-----------------------------------------  个人信息 --------------------------------------------
;客户资料修改
HS831029=client_id|regcust_id|qq|address|zipcode|city_no|e_mail|degree_code|profession_code|home_tel|office_tel|mobile_tel|develop_source|corp_client_group|asset_level|fax|officeaddress|officezip|msn_id|skype_id|developer|cost_place|company_name|industry_range|benefit_person|job_title|certificate_id|certificate_type|work_date|id_kind|id_no|client_name|full_name|id_begindate|id_enddate|client_gender|birthday|id_address|issued_depart|sec_relation_name|sec_relation_phone|socialral_type|nationality|income|child_flag|child_id|statement_flag|risk_info|control_person|industry_type|en_contact_type|contact_freq|nation_id|op_station|
;客户邮寄对账信息设置
HS831146=client_id|fund_account|receive_name|mobile_tel|e_mail|address|zipcode|remark|op_station|
;客户邮寄对账信息获取
HS831147=client_id|fund_account|op_station|
;查询营业部信息
HS830001=branch_no|branch_type|local_no|op_station|
;数据字典查询
HS830002=branch_no|dict_entry|subentry|op_station|
;通用业务办理状态查询
HS831042=client_id|busin_op_type|acpt_id|op_station|
;----------------------------------------- 本次新增的接口 --newnewnew--------------------------------------------
CONTRACT_SIGN=/yjbapi/counter/web/contract/sign|client_id|fund_account|op_entrust_way|op_station|password|password_type|group_nos|business_type|econtract_nos|summary|account_type|branch_no|
LOGGER_COLLECT=/logger/collect|client_id|op_station|app_id|os|datetime|event_id|logtype|exchang_type|frompage|noticeid|business_type|business_result|info|event_name|business_type|message|
IFS831000=/yjbapi/ifs/proxy/invoke/HS831000|op_entrust_way|op_station|input_content|account_content|content_type|password|password_type|image_code|op_station|
WA0062=/yjbapi/ifs/proxy/invoke/WA0062|count|op_station|fund_account|password|
CLIENT_LOGIN=/yjbapi/ifs/proxy/clientlogin|password|op_station|account_content|input_content|
		
SZCHANGEPWD_QUERY=/yjbapi/account/csdcweb/stockholder/required/query|client_id|asset_prop|exchange_type|check_sh_reg|op_station|		
SZCHANGEPWD_ACTIVE=/yjbapi/counter/web/activation/sz|fund_account|client_id|op_station|exchange_type|password|password_type|branch_no|stock_account|check_code|op_entrust_way|
SZCHANGEPWD_REPORT=/yjbapi/counter/web/report/sz|fund_account|client_id|op_station|exchange_type|password|password_type|branch_no|stock_account|op_entrust_way|


SMS_PUSH=/yjbapi/core/uas/v2/sms_push|mobile|template_no|expire_minutes|validate_mobile|op_station|
SMS_VALIDATE=/yjbapi/core/uas/v2/validate|mobile|template_no|captcha_code|expire_now|op_station|
THIRD_BANKLIST=/yjbapi/front/bank/queryOpenBankInfoList|over_flag|group_no|business_type|op_station|
THIRD_BANKLIST_CREDIT=/yjbapi/front/bank/queryCreditBankInfoList|over_flag|group_no|business_type|op_station|
THIRD_INDEXQEURY=/yjbapi/account/depository/change/indexquery|client_id|fund_account|asset_prop|password|password_type|op_station|op_entrust_way|
THIRD_INDEXQEURY_CREDIT=/yjbapi/account/depository/change/indexqueryforcredit|client_id|fund_account|asset_prop|password|password_type|op_station|op_entrust_way|
THIRD_CANCEL=/yjbapi/account/depository/change/cancel|client_id|fund_account|money_type|bank_no|bank_account|bk_password|trans_flag|password|password_type|op_station|op_entrust_way|
THIRD_CANCEL_CREDIT=/yjbapi/account/depository/change/cancelforcredit|client_id|fund_account|money_type|bank_no|bank_account|bk_password|trans_flag|password|password_type|op_station|op_entrust_way|
THIRD_OPEN=/yjbapi/account/depository/change/open|client_id|fund_account|money_type|bank_no|bank_account|bk_password|password|password_type|op_station|op_entrust_way|
THIRD_OPEN_CREDIT=/yjbapi/account/depository/change/openforcredit|client_id|fund_account|money_type|bank_no|bank_account|bk_password|password|password_type|op_station|op_entrust_way|
		
		
;-----------------------------------------  线上业务重构 2017-10-31 --------------------------------------------
;--------条件检查
;条件检查
ACU_001=/yjbapi/account/checkup/check|business_type|client_id|fund_account|password|op_station|exchange_type|double_record_id|kzzStockAccounts|
ACU_002=/yjbapi/account/checkup/checkitem|business_type|client_id|fund_account|item_names|password|op_station|

;--普通A股账户状态查询
SNS_001=/yjbapi/account/csdcweb/stockholder/normal/status/info|client_id|fund_account|password|op_entrust_way|op_station|

;--------风险警示板
;风险警示状态查询
CWRQ_001=/yjbapi/counter/web/riskwarn/querystatus|client_id|fund_account|password|op_entrust_way|op_station|
;风险警示权限开通
CWRQ_002=/yjbapi/counter/web/riskwarn/openright|client_id|fund_account|exchange_type|stock_account|password|op_entrust_way|op_station|
;风险警示权限注销
CWRQ_003=/yjbapi/counter/web/riskwarn/closeright|client_id|fund_account|exchange_type|stock_account|op_entrust_way|op_station|password|


;--------修改密码

;--------个人信息
;客户信息查询
;HS831003=client_id|op_station|
CWAUQ=/yjbapi/counter/web/account/userinfo/query|client_id|fund_account|branch_no|password|op_station|
CWAUQ_001=/yjbapi/counter/web/account/userinfo|client_id|op_station|op_entrust_way|password|password_type|page_type|
CWCPQ=/yjbapi/counter/web/client/prefer/query|client_id|fund_account|branch_no|op_station|op_entrust_way|password|password_type|

;修改用户信息
;ACT_0010=/yjbapi/user/account/userInfoUpdate|fund_account|mobile|tel|address|password|op_station|
CWAUU=/yjbapi/counter/web/account/info/update|client_id|fund_account|op_station|branch_no|op_entrust_way|password|password_type|client_name|id_no|id_kind|id_begindate|id_enddate|id_address|issued_depart|qq|mobile_tel|telephone|address|basic_address|zipcode|e_mail|sec_relation_name|sec_relation_phone|socialral_type|home_tel|province_code|city_code|county_code|profession_code|control_person|controller_name_other|controller_id_kind|controller_id_no|controller_id_begindate|controller_id_enddate|controller_tel|controller_addr|controller_province_code|controller_city_code|controller_county_code|controller_email|controller_profession_code|benefit_person|beneficiary_name_other|beneficiary_id_kind|beneficiary_id_no|beneficiary_id_begindate|beneficiary_id_enddate|beneficiary_tel|beneficiary_addr|beneficiary_province_code|beneficiary_city_code|beneficiary_county_code|beneficiary_email|beneficiary_profession_code|credit_record|tax_resident_person|

;保存客户纳税信息
;UACT_027=/yjbapi/user/account/addSyncTaxInfo
YUAAU=/yjbapi/user/account/addSyncTaxInfo|op_station|client_id|fund_account|password|password_typ|tax_denizen_kind|last_name|first_name|person_name_type|birth_country_code|birth_country_name|birth_address|current_country_code|current_country_name|current_province_code|current_province_name|current_city_code|current_city_name|current_city_name_eng|current_county_code|current_county_name|address_type|current_address|current_address_eng|en_tax_resident_country|en_tax_resident_country_name|nationality_str|taxpayer_identity_number|no_identity_reason|no_identity_remark|

;受益人，控制人，诚信记录查询
;UACT_USERINFOQUERY=/yjbapi/front/counter/account/info/query|client_id|fund_account|op_station|
;受益人，控制人，诚信记录更新
;UACT_USERINFOUPDATE=/yjbapi/front/counter/account/info/edit|client_id|fund_account|op_station|branch_no|control_person|controller_id_no|controller_id_kind|controller_id_begindate|controller_id_enddate|controller_tel|benefit_person|beneficiary_id_no|beneficiary_id_kind|beneficiary_id_begindate|beneficiary_id_enddate|beneficiary_tel|credit_record|

;-------港股通
CWHQS=/yjbapi/counter/web/hkstock/querystatus|client_id|fund_account|password|op_entrust_way|op_station|
CWSHSO=/yjbapi/counter/web/shHkStock/openright|client_id|fund_account|exchange_type|stock_account|password|op_entrust_way|op_station|
CWSZSO=/yjbapi/counter/web/szHkStock/openright|client_id|fund_account|exchange_type|stock_account|password|op_entrust_way|op_station|
CWSHSC=/yjbapi/counter/web/shHkStock/closeright|client_id|fund_account|exchange_type|stock_account|op_entrust_way|op_station|
CWSZSC=/yjbapi/counter/web/szHkStock/closeright|client_id|fund_account|exchange_type|stock_account|op_entrust_way|op_station|

CWEHP=/yjbapi/counter/web/eligtest/haspass|client_id|paper_type|op_entrust_way|op_station|

;-------创业板
CWCNI=/yjbapi/counter/web/chinext/info|client_id|fund_account|op_station|op_entrust_way|password|
CWCNT=/yjbapi/counter/web/chinext/transfer|client_id|fund_account|op_station|op_entrust_way|stock_account|gen_train_flag|password|

;-------退市整理
CWDQS=/yjbapi/counter/web/delist/querystatus|client_id|fund_account|op_station|password|op_entrust_way|
CWDOR=/yjbapi/counter/web/delist/openright|client_id|fund_account|exchange_type|stock_account|password|op_entrust_way|op_station|
CWDCR=/yjbapi/counter/web/delist/closeright|client_id|fund_account|exchange_type|stock_account|op_entrust_way|op_station|

;-------风险测评
;FXCP_001 风险测评试题查询
FXCP_001=/yjbapi/front/counter/paper/query|paper_type|organ_flag|prodta_no|op_station|
;FXCP_002 风险评测答案提交
FXCP_002=/yjbapi/front/counter/paper/submit|branch_no|client_id|paper_answer|paper_type|prodta_no|prod_account|remark|op_station|

QCQ=/yjbapi/questionnaire/content/query|questionnaire_id|op_station|
QHQQ=/yjbapi/questionnaire/had_qualified_question|questionnaire_id|fund_account|account_type|user_account|
QQAQ=/yjbapi/questionnaire/query/allow_submit_today|questionnaire_id|user_account|account_type|
QCSQ=/yjbapi/questionnaire/count/score|questionnaire_id|user_answers|
QRS=/yjbapi/questionnaire/result/save|questionnaire_id|user_answers|op_station|
QRMS=/yjbapi/questionnaire/result/multi/save|questionnaire_id|user_answers|to_local|to_counter|branch_no|client_name|client_id|id_kind|id_no|paper_type|organ_flag|op_station|
CWRQ=/yjbapi/counter/web/risklevel/query|client_id|password|password_type|op_entrust_wag|op_station|
QCQPRESAVE=/yjbapi/questionnaire/result/pre/save|user_answers|branch_no|client_id|paper_type|op_station|questionnaire_id|
QRQ=/yjbapi/questionnaire/result/query|questionnaire_id|user_account|account_type|page_row|page_size|
QCWCQEQ=/yjbapi/counter/web/question/elig/query|branch_no|op_entrust_way|op_station|client_id|password|password_type|paper_type|position_str|request_num|sub_paper_type|forth_days|

;-------指定交易
CWS_001=/yjbapi/counter/web/secuRegTrade|client_id|fund_account|stock_account|password|op_station|

;-------三方存管银行
BDQ_001=/yjbapi/bstransfer/depositebanks/query|branch_no|client_id|fund_account|password|password_type|asset_prop|

;-------开放式基金
CWOI=/yjbapi/counter/web/ofstockholder/info|client_id|fund_account|password|password_type|op_entrust_way|op_station|branch_no|

;-------信用A股
ACSCSI=/yjbapi/account/csdcweb/stockholder/crdt/status/info|client_id|fund_account|password|op_entrust_way|op_station|
ACSCO=/yjbapi/account/csdcweb/stockholder/crdt/open|client_id|fund_account|password|op_entrust_way|op_station|open_type|

;银行卡bin校验
CCBQ=/yjbapi/core/chinapay/bin_query|card_no|op_station|


;-------清密
;客户存在性校验（原子接口）
REPWD_000=/yjbapi/counter/web/exist|fund_account|id_kind|id_no|op_station|
;客户存在性校验
REPWD_001=/yjbapi/counter/web/repwd/exist|fund_account|id_kind|id_no|captcha_id|captcha_code_input|op_station|
;客户审核状态查询
REPWD_002=/yjbapi/counter/web/repwd/applystatus|client_id|op_station|
;创建预约号
REPWD_003=/yjbapi/counter/web/pre/create|business_type|account_type|account_code|op_station|
;提交预约信息
REPWD_004=/yjbapi/counter/web/repwd/preinfo|serial_no|business_type|client_id|fund_account_input|repwd_fund_account|id_kind|id_no|mobile_tel_type|is_mobile_tel_valid|mobile_tel|is_face_recongnition|face_recongnition_score|password|fund_password|op_station|terminal_type|busi_param|submit_flag|mobile_tel|address|profession_code|profession_name|income|income_name|degree_code|degree_name|benefit_person|beneficiary_name_other|beneficiary_id_begindate|beneficiary_id_enddate|beneficiary_id_kind|beneficiary_id_no|beneficiary_tel|beneficiary_addr|beneficiary_profession_code|beneficiary_profession_name|beneficiary_email|control_person|controller_name_other|controller_id_begindate|controller_id_enddate|controller_id_kind|controller_id_no|controller_tel|controller_addr|controller_profession_code|controller_profession_name|controller_email|tax_resident_person|last_name|first_name|birth_country_code|birth_country_name|birth_address|current_country_code|current_country_name|current_address|current_address_eng|en_tax_resident_country|en_tax_resident_country_name|taxpayer_identify_number|no_identityno_reason|no_identityno_remark|
;查询预约信息
REPWD_005=/yjbapi/counter/web/repwd/querypreinfo|serial_no|business_type|
;提交预约档案base64
REPWD_006=/yjbapi/counter/web/archive/uploadbase64|serial_no|business_type|archive_type|archive_file_base64|is_recongnize|op_station|account_type|archive_version|
;提交预约档案fileId
REPWD_007=/yjbapi/counter/web/archive/savefileid|serial_no|business_type|archive_type|file_id|op_station|
;人脸识别接口封装
REPWD_008=/yjbapi/counter/web/face/recongnize|serial_no|business_type|file_id_a|file_id_b|op_station|
;提交重置密码审核申请
REPWD_009=/yjbapi/counter/web/repwd/apply|serial_no|business_type|
;清密客户信息检查
REPWD_010=/yjbapi/counter/web/repwd/clientinfo/check|client_id|op_station|op_entrust_way|branch_no|fund_account|

;用户信息查询
REPWD_100=/yjbapi/user/account/userInfoQuery|fund_account|client_id|


;清密--验证验证码ID
REPWD_UAS_002=/yjbapi/core/uas/validate|captcha_id|captcha_code|expire_now|op_station|
;清密--申请验证码ID
REPWD_UAS_003=/yjbapi/core/uas/id/new|len|complex_random|char_len|int_len|op_station|
;清密--获取验证码图片base64值
REPWD_UAS_006=/yjbapi/core/uas/base64_img|captcha_id|width|height|font_size|font_color|bg_color|noise_color|op_station|
;清密--发送短信验证码
REPWD_SMS_PUSH=/yjbapi/core/uas/v2/sms_push|mobile|template_no|expire_minutes|validate_mobile|op_station|
;清密--验证短信验证码
REPWD_VALIDATE=/yjbapi/core/uas/v2/validate|mobile|template_no|captcha_code|expire_now|op_station|
;清密--公安接口服务
POLICE_001=/yjbapi/core/police/singleCheck|name|personalId|flag|
POLICE_003=/yjbapi/core/police/checkWithHeadPortrait|name|idNo|fileid|flag|
POLICE_004=/yjbapi/core/police/check/autoswitch|name|idNo|fileid|flag|
POLICE_005=/yjbapi/account/police/check|name|idNo|photoId|cacheFlag|cacheValid|saveFlag|scene|

;业务状态服务
PROCESEE_QUERY=/yjbapi/account/processcode/query|business_type|serial_no|op_station|
PROCESEE_UPDATE=/yjbapi/account/processcode/update|business_type|serial_no|step_code|step_value|op_station|

getAuthtoken=/yjbapi/authtoken/get|

;-------CDR与创新企业股票权限 ******** ********
AIEC_01=/yjbapi/account/innovation/enterprise/cdr/querystatus|client_id|fund_account|op_station|op_entrust_way|password|
AIEC_02=/yjbapi/account/innovation/enterprise/cdr/open|branch_no|client_id|fund_account|exchange_type|stock_account|op_station|op_entrust_way|password|
AIEC_03=/yjbapi/account/innovation/enterprise/cdr/close|branch_no|client_id|fund_account|exchange_type|stock_account|op_station|op_entrust_way|password|

AIES_01=/yjbapi/account/innovation/enterprise/stock/querystatus|client_id|fund_account|op_station|op_entrust_way|password|
AIES_02=/yjbapi/account/innovation/enterprise/stock/open|branch_no|client_id|fund_account|exchange_type|stock_account|op_station|op_entrust_way|password|
AIES_03=/yjbapi/account/innovation/enterprise/stock/close|branch_no|client_id|fund_account|exchange_type|stock_account|op_station|op_entrust_way|password|

;-------省市区数据字典
;省
LN_01=/yjbapi/lbs/nation/provinceList|name|
;市
LN_02=/yjbapi/lbs/nation/cityList|parentId|name|
;县
LN_03=/yjbapi/lbs/nation/districtList|parentId|name|

;-------债券逆回购  2018-08-06
RRRQ=/yjbapi/reverse/repos/right/querystatus|client_id|fund_account|op_station|op_entrust_way|password|
RRRO=/yjbapi/reverse/repos/right/open|client_id|fund_account|op_station|op_entrust_way|password|branch_no|exchange_type|stock_account|
RRRC=/yjbapi/reverse/repos/right/close|client_id|fund_account|op_station|op_entrust_way|password|branch_no|exchange_type|stock_account|

CRRQ=/yjbapi/counter/web/reverse/repos/right/querystatus|client_id|fund_account|op_station|op_entrust_way|password|
CRRO=/yjbapi/counter/web/reverse/repos/right/open|client_id|fund_account|op_station|op_entrust_way|password|branch_no|exchange_type|stock_account|
CRRC=/yjbapi/counter/web/reverse/repos/right/close|client_id|fund_account|op_station|op_entrust_way|password|branch_no|exchange_type|stock_account|

;-------沪伦通CDR  2018-11-06
ALCQ=/yjbapi/account/london/cdr/querystatus|client_id|fund_account|op_station|op_entrust_way|password|
ALCO=/yjbapi/account/london/cdr/open|branch_no|client_id|fund_account|exchange_type|stock_account|op_station|op_entrust_way|password|
ALCC=/yjbapi/account/london/cdr/close|branch_no|client_id|fund_account|exchange_type|stock_account|op_station|op_entrust_way|password|

;-------预约销户   2018-12-10
ACA=/yjbapi/account/cancellation/apply|client_id|serial_no|questionnaire_id|mobile|user_answers|apply_flag|op_station|
ACQ=/yjbapi/account/cancellation/query|client_id|serial_no|op_station|
ACAC=/yjbapi/account/cancellation/accountquery|client_id|serial_no|op_station|
ACPC=/yjbapi/account/cancellation/police/check|name|personalId|serial_no|op_station|

;ACAS=/yjbapi/account/cancellation/archive/savefileid
ACC=/yjbapi/account/cancellation/cancelapply|client_id|serial_no|op_station|
ACVC=/yjbapi/account/cancellation/video/createprocess|process_type|identity|identity_type|busi_sn|op_station|serial_no|video_witness_type|

CWAUB64=/yjbapi/counter/web/archive/uploadbase64|serial_no|business_type|archive_type|archive_file_base64|is_recongnize|op_station|account_type|op_station|archive_version|
YCWAQ=/yjbapi/counter/web/archive/queryfileid|serial_no|business_type|archive_type|archive_version|

VACWU=/yjbapi/videodata/account/cancellation/witnesstype/update|process_id|video_witness_type|
VDACQ=/yjbapi/videodata/account/cancellation/query|identity|identity_type|process_type|process_id|op_station|
VDACHBC=/yjbapi/videodata/account/cancellation/heartbeat/check|process_id|op_station|
VDACC=/yjbapi/videodata/account/cancellation/create|process_type|identity|identity_type|busi_sn|op_station|
;-------开户视频 测试用
;account.ops.applyMatch=/yjbapi/account/openstock/match/apply|preengage_id|ip_address|terminal_type|
;account.ops.matchResult=/yjbapi/account/openstock/match/result|preengage_id|request_id|
;video.match.witnessStatus=/yjbapi/core/video/match/witness_status
;operator.queryVideoReject=/yjbapi/core/operator/video/reject/query


;-------科创板  2019-03-09
;ASRQ=/yjbapi/account/london/cdr/querystatus|client_id|fund_account|op_station|op_entrust_way|password|
;ASRO=/yjbapi/account/london/cdr/open|branch_no|client_id|fund_account|exchange_type|stock_account|op_station|op_entrust_way|password|

ASRQ=/yjbapi/account/stib/right/querystatus|client_id|fund_account|op_station|op_entrust_way|password|
ASRO=/yjbapi/account/stib/right/open|branch_no|client_id|fund_account|asset_prop|exchange_type|stock_account|op_station|op_entrust_way|password|
;ASRC=/yjbapi/account/stib/right/close|branch_no|client_id|fund_account|exchange_type|stock_account|op_station|op_entrust_way|password|


ACDS=/yjbapi/account/contract/dontsignufdb/sign_by_groupno|account_type|account_id|group_nos|business_flag|op_station|branch_no|client_id|op_entrust_way|password|password_type|
ACGS=/yjbapi/account/contract/get_client_contract_signed|account_id|account_type|business_flag|econtract_name|page_row|page_size|
ACGC=/yjbapi/account/contract/get_contract|version|contract_no|data_channel|
IMAAQ=/yjbapi/ims-middleware-agreement/agreement/query|channel|agreementNo|agreementVersion|
IMAASCQ=/yjbapi/ims-middleware-agreement/agreement/sign/content/query_by_sign_id|signId|agreementNo|agreementVersion|

;-------北交所  20210-09-17
;北交所交易权限查询
CWSRBI_01=/yjbapi/counter/web/stock/rotation/bjs/info|branch_no|client_id|fund_account|password|password_type|op_entrust_way|op_station|
;北交所交易权限开通并推动待办	
CWSRBOS_02=/yjbapi/counter/web/stock/rotation/bjs/open/submit|todoId|branch_no|client_id|fund_account|exchange_type|sst_report_type|busi_param|password|password_type|op_entrust_way|op_station|
;北交所交易权限注销
CWSRBC_03=/yjbapi/counter/web/stock/rotation/bjs/close|branch_no|client_id|fund_account|exchange_type|sst_report_type|password|password_type|op_entrust_way|op_station|

;------信用北交所  2022-12-09
;信用北交所权限查询
CWSRCBI=/yjbapi/counter/web/stock/rotation/crdit/bjs/info|branch_no|client_id|fund_account|password|password_type|op_station|op_entrust_way|
;北交所权限特转a账户查询
CWTQ=/yjbapi/counter/web/tza/query|client_id|op_station|password|password_type|
;信用北交所权限开通并推动待办
CWCSRBOS=/yjbapi/counter/web/credit/stock/rotation/bjs/open/submit|todoId|branch_no|client_id|fund_account|exchange_type|stock_account|relationship_acct|busi_param|password|password_type|op_entrust_way|op_station|

;新债 2022-05-01
;001. 普通资金账户新债权限权限状态查询
CWCSINFO=/yjbapi/counter/web/conversion/stock/info|branch_no|client_id|fund_account|op_station|op_entrust_way|password|
;002 信用资金账户新债权限权限状态查询
CWCSCINFO=/yjbapi/counter/web/conversion/stock/credit/info|branch_no|client_id|fund_account|op_station|op_entrust_way|password|
;003  新债权限开通并推动待办
CWCSOPEN=/yjbapi/counter/web/conversion/stock/open/submit|todoId|branch_no|client_id|fund_account|exchange_type|sst_report_type|busi_param|password|password_type|op_entrust_way|op_station|stock_account|
;004  新债权限注销
CWCSCLOSE=/yjbapi/counter/web/conversion/stock/close|todoId|branch_no|client_id|fund_account|exchange_type|sst_report_type|busi_param|password|password_type|op_entrust_way|op_station|stock_account|

;查询信用资金账号
FUNDACQUERY=/yjbapi/account/margin/fundaccount/query|client_id|fund_account|credit_fund_account|op_station|password|


;COMM_007 柜台密码校验
CWFCPC=/yjbapi/counter/web/func/client/pwd/chk|client_id|fund_account|branch_no|op_station|op_entrust_way|password|password_type|
;UACT_026 客户资金账号密码独立性查询
UAGPDF=/yjbapi/user/account/getPwdIndependenceFlag|client_id|fund_account|


;-------活动接口  2019-03-09
activity.common.get_all=/yjbapi/activity/common/get_all|collection|json|
activity.common.put=/yjbapi/activity/common/put|collection|json|

;-------下挂封闭式基金户  2019-03-25
ACSF_01=/yjbapi/account/csdcweb/stockholder/fund/status/info|client_id|fund_account|password|op_station|op_entrust_way|
ACSF_02=/yjbapi/account/csdcweb/stockholder/fund/list/info|client_id|fund_account|op_station|op_entrust_way|password|exchange_type|
ACSF_03=/yjbapi/account/csdcweb/stockholder/fund/open|client_id|fund_account|op_station|op_entrust_way|password|sh_open_type|sh_stock_account|sz_open_type|sz_stock_account|

MACST=/yjbmobile/api/core/servertime||

;-------预约业务/特殊视频业务  2019-07-04
;流程详情查询接口
YPMD=/yjbapi/process/middleware/query/detais|process_id|operator|
;待办列表查询
RESERVE_02=/yjbapi/todo/middleware/todoinfo/querylist|todo_type|user_type|user_id|todo_status|op_station|
;待办详情查询
RESERVE_03=/yjbapi/todo/middleware/todoinfo/querynormal|todoId|op_station|

;双向视频任务列表查询
YVWQ=/yjbapi/video/witness/query|busi_type|identity|identity_type|busi_sn|state|video_task_id|

;双向视频任务见证类型更新
YVWTU=/yjbapi/video/witness/type/update|video_task_id|video_witness_type|watermark_flag|

;双向视频任务心跳查询
YVWHC=/yjbapi/video/witness/heartbeat/check|video_task_id|
;双向视频任务创建
YVWC=/yjbapi/video/witness/create|busi_type|identity|identity_type|busi_sn|busi_param|video_witness_type|watermark_flag|


;预约业务-完成身份证上传
RESERVE_04=/yjbapi/todo/middleware/archive/idcardsubmit|op_station|serial_no|business_type|personalId|name|busi_param|todoId|
;预约业务-普通档案上传完成
RESERVE_05=/yjbapi/todo/middleware/archive/commonsubmit|op_station|serial_no|business_type|personalId|name|busi_param|todoId|archive_type|
;预约业务-身份证档案上传嵌入身份证更新任务节点待办完成
YTMAIDS=/yjbapi/todo/middleware/archive/idcardUpdateSubmit|op_station|serial_no|business_type|personalId|name|busi_param|todoId|policeFlag|policeFileId|from_idcard_update_page|
;预约业务-身份证更新状态查询
YAAIDCS=/yjbapi/account/archive/idupdate/checkstatus|client_id|
;预约业务-提交申请
YAAIDSA=/yjbapi/account/archive/idupdate/submitapplication|client_id|id_no|client_name|id_begindate|id_enddate|id_address|issued_depart|file_id_a|file_id_b|file_id_auxiliarys|op_station|terminal_type|

;产品购买双录列表查询
DRQL=/yjbapi/product/purchase/doublerecord/querylist|fund_account|double_record_id|
;历史审核通过双录查询
HSDRQL=/yjbapi/product/purchase/historysuccess/doublerecord/querylist|fund_account|
;预约单创建
CPCBI=/yjbapi/operate/account/createPreCommonBusinessInfo|op_station|clientId|businessType|fundAccount|userType|userId|clientName|busiParam|remark|opUserId|

;合格投资者列表查询
;合格投资者权限查询
IVQL=/yjbapi/qualified/investor/right/query|branch_no|client_id|fund_account|password|password_type|op_entrust_way|op_station|
;合格投资者预约单创建
CPEBI=/yjbapi/operate/account/createPreEIBusinessInfo|clientId|businessType|fundAccount|checkBusinessType|password|op_station|opEntrustWay|userType|userId|clientName|busiParam|remark|opUserId|branch_no|
;资管合格投资者开通并推动待办
IVQO=/yjbapi/qualified/investor/right/qualifications/open/submit|todoId|business_type|branch_no|client_id|fund_account|busi_param|password|password_type|op_entrust_way|op_station|business_type|
;私募合格投资者开通并推动待办
IVPO=/yjbapi/qualified/investor/right/privateplacement/open/submit|todoId|business_type|branch_no|client_id|fund_account|busi_param|password|password_type|op_entrust_way|op_station|
;验证手机号码正确并推动待办
VDSB=/yjbapi/todo/middleware/validate/submit|todoId|mobile|template_no|captcha_code|expire_now|busi_param|op_station|
;新增协议并推动待办
TMSS=/yjbapi/todo/middleware/signtocounter/submit|todoId|account_type|fund_account|group_nos|client_id|branch_no|password|op_station|busi_param|

;反洗钱-存量客户信息确认
;获取动态问卷内容
QCQP=/yjbapi/questionnaire/content/query_process|questionnaire_id|param_mapping|op_station|

;-------股票期权  2019-11-22
;股票期权账户状态查询
ACSO_01=/yjbapi/account/csdcweb/stockholder/option/status/info|client_id|fund_account|password|op_station|
;期权待办创建 通用预约单创建
ACSO_02=/yjbapi/operate/account/createPreReplicationBusinessInfo|clientId|businessType|fundAccount|op_station|opEntrustWay|userType|userId|clientName|busiParam|remark|opUserId|branch_no|password|
;待办流程更新
ACSO_03=/yjbapi/todo/middleware/todoinfo/update|todoId|busi_param|busi_op_type|op_type|option_type|op_station|


;-------反洗钱  2019-12-30
LLRM=/yjbapi/lbs/location/registration/match|phone|location_code|match_rule|branch_no|op_station|
LLRMV2=/yjbapi/lbs/location/registration/match/v2|phone|address_code|match_rule|branch_no|op_station|

;-------新三板
;新三板交易权限查询
CWSR_01=/yjbapi/counter/web/stock/rotation/info|client_id|fund_account|password|op_station|branch_no|password_type|op_entrust_way|
;新三板交易权限开通并推动待办
CWSR_02=/yjbapi/counter/web/stock/rotation/open/submit|client_id|fund_account|password|op_station|branch_no|password_type|op_entrust_way|todoId|exchange_type|stock_account|sst_report_type|busi_param|
;新三板交易权限注销
CWSR_03=/yjbapi/counter/web/stock/rotation/close|client_id|fund_account|password|op_station|branch_no|password_type|op_entrust_way|exchange_type|stock_account|sst_report_type|
;提交问卷并推动待办
RESERVE_06=/yjbapi/todo/middleware/multi/save/submit|op_station|todoId|busi_param|questionnaire_id|user_answers|to_local|to_counter|branch_no|client_name|client_id|id_no|id_kind|paper_type|organ_flag|

;-------签约标准咨询服务
IAB_01=/yjbapi/iadviser/basic/query_sign_status|client_id|op_station|
;IAB_02=/yjbapi/inverstment/adviser/appropriate/check|client_id|op_station|branch_no|op_entruset_way|password|prod_no|password_type|
IAB_02=/yjbapi/iadviser/sign/appropriate_check|client_id|op_station|branch_no|op_entruset_way|password|prod_no|password_type|
IAB_03=/yjbapi/todo/middleware/signStandard/submit|todoId|account_type|fund_account|group_nos|client_id|branch_no|password|op_station|busi_param|contract_no|

;-------开通创业板
CWCNO=/yjbapi/counter/web/chinext/openGem|client_id|fund_account|password|op_station|branch_no|password_type|op_entrust_way|todoId|exchange_type|stock_account|sst_report_type|busi_param|stock_account|
;-------信用创业板-2020-05-14
CWCCNI=/yjbapi/counter/web/credit/chinext/info|client_id|fund_account|op_station|op_entrust_way|password|
;CWCCNI=/yjbapi/counter/web/chinext/info|client_id|fund_account|op_station|op_entrust_way|password|
CWCCNO=/yjbapi/counter/web/credit/chinext/openGem|client_id|fund_account|password|op_station|branch_no|password_type|op_entrust_way|todoId|exchange_type|busi_param|stock_account|business_type|

;-------创业板7*24 ********
TMDSS=/yjbapi/todo/middleware/dontsignufdb/sign/submit|todoId|account_type|account_id|group_nos|client_id|branch_no|password|op_station|busi_param|contract_no|

ACT_0015=/yjbapi/account/contract/sign_status_by_accountid|account_id|account_type|econtract_no|

;-------五四调佣确认 ********
PACR=/yjbapi/ims-api/price/adjust/confirm/result|applyId|clientId|op_station|
PACC=/yjbapi/ims-api/price/adjust/confirm/commissionInfo|applyId|clientId|op_station|

;-------用户个人资料与中登不一致 是否签署问卷查询 ********
CWCMEQ=/yjbapi/counter/web/csdc/message/exit/query|questionnaire_id|client_id|user_account|account_type|push_cust_type|csdc_message_version|op_station|

;-------开户电话回访预约 ********
ACCAPRE=/yjbapi/account/callout/preCommit|client_id|password|op_station|

;-------专项头寸权限 ********
CWSSI=/yjbapi/counter/web/special/securities/info|branch_no|client_id|fund_account|password|op_station|password_type|op_entrust_way|
CWSSOS=/yjbapi/counter/web/special/securities/open/submit|todoId|busi_param|branch_no|client_id|fund_account|password|op_station|begin_date|end_date|role_rights|password_type|op_entrust_way|
CWSSC=/yjbapi/counter/web/special/securities/close|branch_no|client_id|fund_account|password|op_station|password_type|op_entrust_way|

;-------补开股东户 ********
TIME_01=/yjbmobile/api/core/servertime|op_station|
TIME_02=/yjbapi/core/timeservice/checkTransactionTime|type|exchange_type|time_kind|op_station|
CWI_01=/yjbapi/counter/web/identification|client_id|hack_flag|detect_str|serial_no|business_type|op_station|
TMSS_01=/yjbapi/counter/web/singlevideo/save|todoId|serial_no|business_type|archive_type|file_id|storage_type|storage_address|busi_param|op_station|

;-------修改密码 ********
YCWF_01=/yjbapi/counter/web/fundaccount/query|op_client_id|op_fund_account|
YCWFPU_01=/yjbapi/counter/web/fundaccount/password/update|op_client_id|op_fund_account|old_password|password_type|new_password|op_entrust_way|op_station|

;-------关联关系确认 ********
YCWIRC_01=/yjbapi/counter/web/incidence/relation/createPreReplicationBusinessInfo|clientId|businessType|password|fundAccount|op_station|branch_no|opEntrustWay|userType|userId|clientName|busiParam|
YCWIRI_01=/yjbapi/counter/web/incidence/relation/info|branch_no|client_id|fund_account|password|op_station|password_type|op_entrust_way|
YCWIRAI_01=/yjbapi/counter/web/incidence/relation/apply/info|serial_no|force|op_station|

;-------业务办理进度查询 ********
BTPS=/yjbapi/counter/web/business/rate|client_id|op_station|business_type|

;-------首页视图 ********
YCWBMQ=/yjbapi/counter/web/business/menu/query|channel_code|

XYBJSDJG=/yjbapi/counter/web/credit/bjs/investigation/info/submit|todoId|client_id|shareholder_flag|senior_person_flag|restrict_secu_flag|relatedholder_flag|relatedholder_info|restrict_secu_info|senior_person_info|shareholder_info|busi_param|op_entrust_way|op_station|

;-------经纪业务管理办法
CWCRQ=/yjbapi/counter/web/report/query|client_id|
CWCRCQ=/yjbapi/counter/web/report/commit/questionnaire|report_id|answer_id|

;-------重庆大盘手双元营业部活动
CRHWT_8001=/thirdpart/cairenhui/wt-new/apache-tomcat-wtapi/CRH-WT8001.json|client_id|fund_account|branch_no|op_station|op_entrust_way|password|email|business_start_date|business_end_date|
CRHWT_8002=/thirdpart/cairenhui/wt-new/apache-tomcat-wtapi/CRH-WT8002.json|client_id|start_date|end_date|

;-------可转债退市整理权限
CWSRKI=/yjbapi/counter/web/stock/rotation/kzztszl/info|branch_no|client_id|fund_account|password|password_type|op_entrust_way|op_station|
CWSRKC=/yjbapi/counter/web/stock/rotation/kzztszl/close|branch_no|client_id|fund_account|exchange_type|stock_accounts|password|password_type|op_entrust_way|op_station|
CWSRKOS=/yjbapi/counter/web/stock/rotation/kzztszl/open/submit|todoId|branch_no|client_id|fund_account|exchange_type|stock_accounts|busi_param|password|password_type|op_entrust_way|op_station|source|

;-------封闭式基金、补开股东户适当性协议
CWCMQ=/yjbapi/counter/web/contract/match/query|client_id|fund_account|op_station|password|

;-------风险测评查询差异
CWECQ=/yjbapi/counter/web/eligtest/compare/query|client_id|fund_account|op_station|password|user_answers|questionnaire_id|questionnaire_version|

;-------获取前端配置
SCBQ=/yjbapi/syscommon/config/business/query|

;-------获取协议组内容
IMABQ=/yjbapi/ims-middleware-agreement/agreement/batch/query|channel|agreementNos|
IMAAS=/yjbapi/ims-middleware-agreement/agreement/sign|channel|agreementNos|accountId|accountType|

;-------查询是否有未完成的销户流程
YACP=/yjbapi/account/cancellation/processQuery|client_id|

";

//解析config配置里面的yjbAPI列表，变成键值对的可调用列表
function initApis($apiparams){
	$str = $apiparams['apistring'];
	$str_array = explode("\n", $str);

	$apis = array();
	foreach($str_array as $sr) {
		if(trim($sr) != "") {// 去除换或其他字符串
			if(substr($sr,0,1) != ";"){ // 非注释
				$_tmp = explode("=", $sr);
				if(count($_tmp) == 2) {
					$_path = explode("|", $_tmp[1]);
					$path = array_shift($_path);
					array_pop($_path);
					$apis[$_tmp[0]] = array("path"=>$path, "params"=>$_path);
				}
			}
		}
	}
	return $apis;
	//$apiparams['apis'] = $apis;
}
// 加载成功后直接执行生成apis列表
$apiparams['apis']=initApis($apiparams);
//print"<pre>";print_r($apiparams['apis']);exit;
return $apiparams;