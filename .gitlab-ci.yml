include:
  - local: .gitlab-ci.moc.yml
# pipeline 名称与顺序 必填
stages:
    - build
    # - deploy
build always:
    extends:
        - .build-moc
    only:
        - release
    when: always
    
build manual:
  extends:
    - .build-moc
  only: 
    - dev-liurunnan
    - dev-2617
    - dev-2604
    - dev-2611
    - /^test-.*$/
    - /^release.*$/
  when: manual
# build manual:
#     extends:
#         - .build-deploy-on-win
#     only:
#         - develop
#     when: manual
